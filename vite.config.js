// @ts-check
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import leanix from 'vite-plugin-lxr'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), leanix()],
  resolve: {
    alias: {
      '@app': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@config': path.resolve(__dirname, './src/config'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@hooks': path.resolve(__dirname, './src/hooks')
    }
  }
})
