name: 🚀 LeanIX Report Release Manager

on:
  workflow_dispatch:
    inputs:
      version:
        description: "New Report Version"
        required: true
        type: string
      deploy:
        description: "Deploy"
        required: true
        type: choice
        options:
          - "BOTH (INTG & PROD)"
          - "NONE"
          - "INTG"
          - "PROD"

jobs:
  build:
    name: "Build and Release"
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Update package.json version
        run: npm version ${{ github.event.inputs.version }} --no-git-tag-version
      - name: Use Node.js 20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
      - name: Install dependencies
        run: yarn install
      - name: Build Report
        run: yarn build
      - name: Commit package.json changes
        run: |
          git config --local user.email "github-actions[bot]@leanix.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add package.json
          git commit -m "chore: bump version to ${{ github.event.inputs.version }}"
      - name: Create and push tag
        run: |
          git tag v${{ github.event.inputs.version }}
          git push origin v${{ github.event.inputs.version }}
          git push

  intg-get-token:
    name: "[INTG] Get OAuth Token"
    uses: "./.github/workflows/_reusable_get_oauth_token.yml"
    needs: build
    if: github.event.inputs.deploy == 'BOTH (INTG & PROD)' || github.event.inputs.deploy == 'INTG'
    secrets:
      leanix_host: ${{ secrets.LEANIX_HOST }}
      api_token: ${{ secrets.LEANIX_API_TOKEN_INTG }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}

  intg-upload:
    name: "[INTG] Upload Report"
    needs: [ build, intg-get-token ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
      - name: Fetch latest tags
        run: |
          git fetch --tags
          git checkout v${{ github.event.inputs.version }}
      - name: Create lxr_prod.json
        run: |
          CLEANED_HOST="${{ secrets.LEANIX_HOST }}"
          CLEANED_HOST="${CLEANED_HOST#https://}"
          echo "{
            \"host\": \"$CLEANED_HOST\",
            \"workspace\": \"${{ secrets.LEANIX_WORKSPACE_INTG }}\",
            \"apitoken\": \"${{ secrets.LEANIX_API_TOKEN_INTG }}\"
          }" > lxr.json
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Get Version
        run: |
          VERSION=$(jq -r '.version' package.json)
          echo "VERSION FOR UPLOAD: $VERSION"
      - name: Upload to LeanIX
        id: upload
        continue-on-error: true
        run: yarn upload
      - name: Handle Version Conflict
        if: steps.upload.outcome == 'failure'
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.intg-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"
          REPORTS_RESPONSE=$(curl -s -X GET \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
            -H "Authorization: Bearer $token")
          
          PACKAGE_NAME=$(jq -r '.name' package.json)
          PACKAGE_VERSION=$(jq -r '.version' package.json)
          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)
          
          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      <<< "$REPORTS_RESPONSE")
          
          REPORT_ID=$(echo "$REPORT" | jq -r '.id')
          
          curl -s -X DELETE \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports/$REPORT_ID" \
            -H "Authorization: Bearer $token"
          
          yarn upload
  intg-get-report:
    name: "[INTG] Get Report Info"
    needs: [ intg-get-token, intg-upload ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    outputs:
      report_id: ${{ steps.report.outputs.report_id }}
      updated_report: ${{ steps.report.outputs.updated_report }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Fetch latest tags and checkout correct version
        run: |
          git fetch --tags
          LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`)
          echo "Latest tag: $LATEST_TAG"
          git checkout $LATEST_TAG
      - name: Verify package.json content
        run: cat package.json
      - name: Get Report Info
        id: report
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.intg-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"
          
          echo "Report processing completed successfully. $token"
          REPORTS_RESPONSE=$(curl -s -X GET \
          "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
          -H "Authorization: Bearer $token")
          
          # Validiere Reports Response
          if ! echo "$REPORTS_RESPONSE" | jq . > /dev/null 2>&1; then
            echo "Error: Invalid JSON response from reports endpoint"
            echo "Response: $REPORTS_RESPONSE"
            exit 1
          fi
          
          # Speichere Reports für Debugging
          echo "$REPORTS_RESPONSE" > reports.json
          
          # Lese und validiere Package Infos
          if [ ! -f "package.json" ]; then
            echo "Error: package.json not found"
            exit 1
          fi
          
          PACKAGE_NAME=$(jq -r '.name' package.json)
          if [ -z "$PACKAGE_NAME" ]; then
            echo "Error: name not found in package.json"
            exit 1
          fi
          
          PACKAGE_VERSION=$(jq -r '.version' package.json)
          if [ -z "$PACKAGE_VERSION" ]; then
            echo "Error: version not found in package.json"
            exit 1
          fi
          
          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)
          if [ -z "$PACKAGE_REPORT_ID" ]; then
            echo "Error: leanixReport.id not found in package.json"
            exit 1
          fi
          
          echo "Looking for report:"
          echo "Name: $PACKAGE_NAME"
          echo "Version: $PACKAGE_VERSION"
          echo "Report ID: $PACKAGE_REPORT_ID"
          
          # Finde passenden Reports
          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      reports.json)
          
          # Validiere gefundenen Report
          if [ -z "$REPORT" ]; then
            echo "Error: No matching report found"
            exit 1
          fi
          
          # Extrahiere und validiere Report ID
          REPORT_ID=$(echo "$REPORT" | jq -r '.id')
          if [ -z "$REPORT_ID" ] || [ "$REPORT_ID" = "null" ]; then
            echo "Error: Invalid report ID"
            exit 1
          fi
          echo "Found report ID: $REPORT_ID"
          echo "report_id=$REPORT_ID" >> $GITHUB_OUTPUT
          
          UPDATED_REPORT=$(echo "$REPORT" | jq -c '.enabled = true')
          UPDATED_REPORT=$(echo "$UPDATED_REPORT" | jq -c '.menuSection = "custom"')
          echo "updated_report=$UPDATED_REPORT" >> $GITHUB_OUTPUT

  intg-enable-report:
    name: "[INTG] Enable Report"
    uses: "./.github/workflows/_reusable_enable_report.yml"
    needs: [ intg-get-token, intg-get-report, intg-upload ]
    with:
      report_id: ${{ needs.intg-get-report.outputs.report_id }}
      updated_report: ${{ needs.intg-get-report.outputs.updated_report }}
    secrets:
      auth_token: ${{ needs.intg-get-token.outputs.token }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}
      leanix_host: ${{ secrets.LEANIX_HOST }}

  #### PROD
  prod-get-token:
    name: "[PROD] Get OAuth Token"
    if: github.event.inputs.deploy == 'BOTH (INTG & PROD)' || github.event.inputs.deploy == 'PROD'
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    needs: build
    outputs:
      token: ${{ steps.prod_get_token.outputs.token }}
    steps:
      - name: Get Token
        id: prod_get_token
        run: |
          TOKEN_RESPONSE=$(curl -s -u "apitoken:${{ secrets.LEANIX_API_TOKEN_PROD }}" \
            --request POST \
            --url "${{ secrets.LEANIX_HOST }}/services/mtm/v1/oauth2/token" \
            --data "grant_type=client_credentials")
          TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
          echo "::add-mask::$TOKEN"
          token_encrypted=$(gpg --symmetric --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "$TOKEN") | base64 -w0)
          echo "token=$token_encrypted" >> $GITHUB_OUTPUT

  prod-upload:
    name: "[PROD] Upload Report"
    needs: [ build, prod-get-token ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
      - name: Fetch latest tags
        run: |
          git fetch --tags
          git checkout v${{ github.event.inputs.version }}
      - name: Create lxr_prod.json
        run: |
          CLEANED_HOST="${{ secrets.LEANIX_HOST }}"
          CLEANED_HOST="${CLEANED_HOST#https://}"
          echo "{
            \"host\": \"$CLEANED_HOST\",
            \"workspace\": \"${{ secrets.LEANIX_WORKSPACE_PROD }}\",
            \"apitoken\": \"${{ secrets.LEANIX_API_TOKEN_PROD }}\"
          }" > lxr.json
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Get Version
        run: |
          VERSION=$(jq -r '.version' package.json)
          echo "VERSION FOR UPLOAD: $VERSION"
      - name: Upload to LeanIX
        id: upload
        continue-on-error: true
        run: yarn upload
      - name: Handle Version Conflict
        if: steps.upload.outcome == 'failure'
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.prod-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"
          REPORTS_RESPONSE=$(curl -s -X GET \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
            -H "Authorization: Bearer $token")

          PACKAGE_NAME=$(jq -r '.name' package.json)
          PACKAGE_VERSION=$(jq -r '.version' package.json)
          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)

          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      <<< "$REPORTS_RESPONSE")

          REPORT_ID=$(echo "$REPORT" | jq -r '.id')

          curl -s -X DELETE \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports/$REPORT_ID" \
            -H "Authorization: Bearer $token"

          yarn upload
  prod-get-report:
    name: "[PROD] Get Report Info"
    needs: [ prod-get-token, prod-upload ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    outputs:
      report_id: ${{ steps.report.outputs.report_id }}
      updated_report: ${{ steps.report.outputs.updated_report }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Fetch latest tags and checkout correct version
        run: |
          git fetch --tags
          LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`)
          echo "Latest tag: $LATEST_TAG"
          git checkout $LATEST_TAG
      - name: Verify package.json content
        run: cat package.json
      - name: Get Report Info
        id: report
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.prod-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"

          echo "Report processing completed successfully. $token"
          REPORTS_RESPONSE=$(curl -s -X GET \
          "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
          -H "Authorization: Bearer $token")

          # Validiere Reports Response
          if ! echo "$REPORTS_RESPONSE" | jq . > /dev/null 2>&1; then
            echo "Error: Invalid JSON response from reports endpoint"
            echo "Response: $REPORTS_RESPONSE"
            exit 1
          fi

          # Speichere Reports für Debugging
          echo "$REPORTS_RESPONSE" > reports.json

          # Lese und validiere Package Infos
          if [ ! -f "package.json" ]; then
            echo "Error: package.json not found"
            exit 1
          fi

          PACKAGE_NAME=$(jq -r '.name' package.json)
          if [ -z "$PACKAGE_NAME" ]; then
            echo "Error: name not found in package.json"
            exit 1
          fi

          PACKAGE_VERSION=$(jq -r '.version' package.json)
          if [ -z "$PACKAGE_VERSION" ]; then
            echo "Error: version not found in package.json"
            exit 1
          fi

          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)
          if [ -z "$PACKAGE_REPORT_ID" ]; then
            echo "Error: leanixReport.id not found in package.json"
            exit 1
          fi

          echo "Looking for report:"
          echo "Name: $PACKAGE_NAME"
          echo "Version: $PACKAGE_VERSION"
          echo "Report ID: $PACKAGE_REPORT_ID"

          # Finde passenden Reports
          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      reports.json)

          # Validiere gefundenen Report
          if [ -z "$REPORT" ]; then
            echo "Error: No matching report found"
            exit 1
          fi

          # Extrahiere und validiere Report ID
          REPORT_ID=$(echo "$REPORT" | jq -r '.id')
          if [ -z "$REPORT_ID" ] || [ "$REPORT_ID" = "null" ]; then
            echo "Error: Invalid report ID"
            exit 1
          fi
          echo "Found report ID: $REPORT_ID"
          echo "report_id=$REPORT_ID" >> $GITHUB_OUTPUT

          UPDATED_REPORT=$(echo "$REPORT" | jq -c '.enabled = true')
          UPDATED_REPORT=$(echo "$UPDATED_REPORT" | jq -c '.menuSection = "custom"')
          echo "updated_report=$UPDATED_REPORT" >> $GITHUB_OUTPUT

  prod-enable-report:
    name: "[PROD] Enable Report"
    needs: [ prod-get-token, prod-get-report ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - name: Enable Report
        run: |
          echo "${{ needs.prod-get-report.outputs.updated_report }}"

          # Decrypt token and mask it
          token=$(echo "${{ needs.prod-get-token.outputs.token }}" | base64 --decode | gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output -)
          echo "::add-mask::$token"

          echo "REPORT ID: ${{ needs.prod-get-report.outputs.report_id }}"
          echo "REPORT DATA:"
          echo "${{ needs.prod-get-report.outputs.updated_report }}"

          # Ensure updated_report is valid JSON
          JSON_DATA=$(echo '${{ needs.prod-get-report.outputs.updated_report }}' | jq -c '.')

          # Make PUT request and capture the response
          RESPONSE=$(curl -s -X PUT \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports/${{ needs.prod-get-report.outputs.report_id }}" \
            -H "Authorization: Bearer $token" \
            -H "Content-Type: application/json" \
            -d "$JSON_DATA")

          # Check if RESPONSE is empty or contains an error
          if [[ -z "$RESPONSE" ]]; then
            echo "Error: No response received from the server. Please check the request details."
            exit 1
          else
            # Display the response
            echo "Response from server:"
            echo "$RESPONSE"
          fi

  intg-remove-old-reports:
    name: "[INTG] Remove old Reports"
    uses: "./.github/workflows/_reusable_remove_unused_reports.yml"
    needs: [intg-enable-report, intg-get-report, intg-get-token]
    if: github.event.inputs.deploy == 'BOTH (INTG & PROD)' || github.event.inputs.deploy == 'INTG'
    with:
      is_intg: true
      delete_all: false
    secrets:
      leanix_host: ${{ secrets.LEANIX_HOST }}
      auth_token: ${{ needs.intg-get-token.outputs.token }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}

  prod-remove-old-reports:
    name: "[PROD] Remove old Reports"
    uses: "./.github/workflows/_reusable_remove_unused_reports.yml"
    needs: [ prod-enable-report, prod-get-report,prod-get-token ]
    if: github.event.inputs.deploy == 'BOTH (INTG & PROD)' || github.event.inputs.deploy == 'PROD'
    with:
      is_intg: false
      delete_all: false
    secrets:
      leanix_host: ${{ secrets.LEANIX_HOST }}
      auth_token: ${{ needs.prod-get-token.outputs.token }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}


  increment-version:
    name: Increment Version for Development
    needs: [ prod-enable-report, intg-enable-report ]
    if: ${{ always() && !failure() && !cancelled() }}
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get latest changes
        run: |
          git pull origin main
          git checkout main
      - name: Get commit hash
        id: commit
        run: echo "hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
      - name: Get current version and increment patch
        run: |
          CURRENT_VERSION="${{ github.event.inputs.version }}"
          IFS='.' read -r -a VERSION_PARTS <<< "$CURRENT_VERSION"
          NEXT_PATCH=$((VERSION_PARTS[2] + 1))
          NEXT_VERSION="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$NEXT_PATCH-SNAPSHOT"
          npm version $NEXT_VERSION --no-git-tag-version
      - name: Commit and push changes
        run: |
          git config --local user.email "github-actions[bot]@leanix.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add package.json
          git commit -m "chore: prepare for next development version"
          git push