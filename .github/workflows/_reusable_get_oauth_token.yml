name: "🔁 Get OAuth Token"

on:
  workflow_call:
    secrets:
      leanix_host:
        required: true
      api_token:
        required: true
      encryption_password:
        required: true
    outputs:
      token:
        value: ${{jobs.get_token.outputs.token}}

jobs:
  get_token:
    name: Get OAuth Token
    runs-on: ubuntu-24.04
    outputs:
      token: ${{steps.get_token.outputs.token}}
    steps:
      - name: Get Token
        id: get_token
        run: |
          TOKEN_RESPONSE=$(curl -s -u "apitoken:${{ secrets.api_token }}" \
            --request POST \
            --url "${{ secrets.leanix_host }}/services/mtm/v1/oauth2/token" \
            --data "grant_type=client_credentials")
          TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
          echo "::add-mask::$TOKEN"
          token_encrypted=$(gpg --symmetric --batch --passphrase "${{ secrets.encryption_password }}" --output - <(echo "$TOKEN") | base64 -w0)
          echo "token=$token_encrypted" >> $GITHUB_OUTPUT