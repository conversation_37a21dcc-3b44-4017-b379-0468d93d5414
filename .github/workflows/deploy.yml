name: Build and Release Cockpit

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      run_build:
        description: 'Start build'
        required: false
        default: true
        type: boolean

jobs:
  build:
    runs-on: ubuntu-24.04

    # Docker Container für Cross-Platform Builds verwenden
    container:
      image: electronuserland/builder:wine

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Wine environment
        run: |
          # Create Wine prefix directory with proper permissions
          export WINEPREFIX=/tmp/.wine
          mkdir -p $WINEPREFIX
          chmod -R 755 $WINEPREFIX

          # Set ownership to current user
          chown -R $(whoami):$(whoami) $WINEPREFIX 2>/dev/null || true

          # Initialize Wine (this may take a moment)
          echo "Initializing Wine..."
          wine --version

          # Set Wine to not show GUI dialogs and run in headless mode
          export WINEDLLOVERRIDES="mscoree,mshtml="
          export DISPLAY=:99

          # Create a dummy registry to avoid permission issues
          echo "Setting up Wine registry..."
          wineboot --init || true

          # Verify Wine is working
          echo "Wine setup complete"
        env:
          WINEPREFIX: /tmp/.wine
          WINEDLLOVERRIDES: "mscoree,mshtml="
          DISPLAY: ":99"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        # Nutzen der Version aus package.json

      - name: Setup Node.js with pnpm cache
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build application
        run: |
          echo "Starting build process..."
          echo "Wine prefix: $WINEPREFIX"
          echo "Current user: $(whoami)"
          echo "Wine version: $(wine --version)"

          # Run the build
          pnpm build:win
        env:
          # Electron Cache für bessere Performance
          ELECTRON_CACHE: "/tmp/.cache/electron"
          ELECTRON_BUILDER_CACHE: "/tmp/.cache/electron-builder"
          # Wine configuration
          WINEPREFIX: /tmp/.wine
          WINEDLLOVERRIDES: "mscoree,mshtml="
          DISPLAY: ":99"
          # Disable code signing for now to avoid Wine issues
          CSC_IDENTITY_AUTO_DISCOVERY: false

      - name: List build outputs
        run: |
          echo "Contents of dist directory:"
          ls -la dist/ || echo "dist directory not found"
          echo "Looking for executable files:"
          find dist/ -name "*.exe" 2>/dev/null || echo "No exe files found"

      - name: Upload Windows Setup Installer
        uses: actions/upload-artifact@v4
        with:
          name: eam-cockpit-setup.exe
          path: dist/*-setup.exe
          retention-days: 30

      - name: Upload Windows Portable Executable
        uses: actions/upload-artifact@v4
        with:
          name: eam-cockpit-portable.exe
          path: dist/*-portable.exe
          retention-days: 30

      - name: Upload Complete Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: eam-cockpit-complete-artifacts
          path: dist/
          retention-days: 30
