name: 🚀 LeanIX Report Snapshot Manager

on:
  push:
    tags-ignore:
      - '**'
    branches:
      - '**'
jobs:
  build:
    name: Build Report
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
          cache: 'yarn'
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Build Report
        run: yarn build

  #  # INTG API Requests
  intg-get-token:
    name: "[INTG] Get OAuth Token"
    uses: "./.github/workflows/_reusable_get_oauth_token.yml"
    needs: build
    secrets:
      leanix_host: ${{ secrets.LEANIX_HOST }}
      api_token: ${{ secrets.LEANIX_API_TOKEN_INTG }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}

  intg-upload:
    name: "[INTG] Upload Report"
    needs: [ build, intg-get-token ]
    runs-on: cluster-intg
    container:
      image: docker.packages.helvetia.com/node:20
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0
          cache: 'yarn'
      - name: Create lxr_prod.json
        run: |
          CLEANED_HOST="${{ secrets.LEANIX_HOST }}"
          CLEANED_HOST="${CLEANED_HOST#https://}"
          echo "{
            \"host\": \"$CLEANED_HOST\",
            \"workspace\": \"${{ secrets.LEANIX_WORKSPACE_INTG }}\",
            \"apitoken\": \"${{ secrets.LEANIX_API_TOKEN_INTG }}\"
          }" > lxr.json
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Ensure jq is installed
        run: |
          if ! command -v jq >/dev/null 2>&1; then
            apt-get update && apt-get install -y jq
          fi
      - name: Get Version
        run: |
          VERSION=$(jq -r '.version' package.json)
          echo "VERSION FOR UPLOAD: $VERSION"
      - name: Upload to LeanIX
        id: upload
        continue-on-error: true
        run: yarn upload
      - name: Handle Version Conflict
        if: steps.upload.outcome == 'failure'
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.intg-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"
          REPORTS_RESPONSE=$(curl -s -X GET \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
            -H "Authorization: Bearer $token")

          PACKAGE_NAME=$(jq -r '.name' package.json)
          PACKAGE_VERSION=$(jq -r '.version' package.json)
          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)

          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      <<< "$REPORTS_RESPONSE")

          REPORT_ID=$(echo "$REPORT" | jq -r '.id')

          curl -s -X DELETE \
            "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports/$REPORT_ID" \
            -H "Authorization: Bearer $token"

          yarn upload

  intg-get-report:
    name: "[INTG] Get Report Info"
    needs: [ intg-get-token, intg-upload ]
    runs-on: ubuntu-22.04
    outputs:
      report_id: ${{ steps.report.outputs.report_id }}
      updated_report: ${{ steps.report.outputs.updated_report }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get Report Info
        id: report
        run: |
          token=$(gpg --decrypt --quiet --batch --passphrase "${{ secrets.ENCRYPTION_PASSWORD }}" --output - <(echo "${{ needs.intg-get-token.outputs.token }}" | base64 --decode))
          echo "::add-mask::$token"
          
          echo "Report processing completed successfully. $token"
          REPORTS_RESPONSE=$(curl -s -X GET \
          "${{ secrets.LEANIX_HOST }}/services/pathfinder/v1/reports?sorting=updatedAt&sortDirection=DESC&pageSize=100" \
          -H "Authorization: Bearer $token")
          
          # Validiere Reports Response
          if ! echo "$REPORTS_RESPONSE" | jq . > /dev/null 2>&1; then
            echo "Error: Invalid JSON response from reports endpoint"
            echo "Response: $REPORTS_RESPONSE"
            exit 1
          fi
          
          # Speichere Reports für Debugging
          echo "$REPORTS_RESPONSE" > reports.json
          
          # Lese und validiere Package Infos
          if [ ! -f "package.json" ]; then
            echo "Error: package.json not found"
            exit 1
          fi
          
          PACKAGE_NAME=$(jq -r '.name' package.json)
          if [ -z "$PACKAGE_NAME" ]; then
            echo "Error: name not found in package.json"
            exit 1
          fi
          
          PACKAGE_VERSION=$(jq -r '.version' package.json)
          if [ -z "$PACKAGE_VERSION" ]; then
            echo "Error: version not found in package.json"
            exit 1
          fi
          
          PACKAGE_REPORT_ID=$(jq -r '.leanixReport.id' package.json)
          if [ -z "$PACKAGE_REPORT_ID" ]; then
            echo "Error: leanixReport.id not found in package.json"
            exit 1
          fi
          
          echo "Looking for report:"
          echo "Name: $PACKAGE_NAME"
          echo "Version: $PACKAGE_VERSION"
          echo "Report ID: $PACKAGE_REPORT_ID"
          
          # Finde passenden Reports
          REPORT=$(jq -r --arg name "$PACKAGE_NAME" \
                      --arg version "$PACKAGE_VERSION" \
                      --arg reportId "$PACKAGE_REPORT_ID" \
                      '.data[] | select(.name == $name and .version == $version and .reportId == $reportId)' \
                      reports.json)
          
          # Validiere gefundenen Report
          if [ -z "$REPORT" ]; then
            echo "Error: No matching report found"
            exit 1
          fi
          
          # Extrahiere und validiere Report ID
          REPORT_ID=$(echo "$REPORT" | jq -r '.id')
          if [ -z "$REPORT_ID" ] || [ "$REPORT_ID" = "null" ]; then
            echo "Error: Invalid report ID"
            exit 1
          fi
          echo "Found report ID: $REPORT_ID"
          echo "report_id=$REPORT_ID" >> $GITHUB_OUTPUT
          
          UPDATED_REPORT=$(echo "$REPORT" | jq -c '.enabled = true')
          UPDATED_REPORT=$(echo "$UPDATED_REPORT" | jq -c '.menuSection = "custom"')
          echo "updated_report=$UPDATED_REPORT" >> $GITHUB_OUTPUT


  intg-enable-report:
    name: "[INTG] Enable Report"
    uses: "./.github/workflows/_reusable_enable_report.yml"
    needs: [ intg-get-token, intg-get-report, intg-upload ]
    with:
      report_id: ${{ needs.intg-get-report.outputs.report_id }}
      updated_report: ${{ needs.intg-get-report.outputs.updated_report }}
    secrets:
      auth_token: ${{ needs.intg-get-token.outputs.token }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}
      leanix_host: ${{ secrets.LEANIX_HOST }}

  intg-remove-old-reports:
    name: "[INTG] Remove old Reports"
    uses: "./.github/workflows/_reusable_remove_unused_reports.yml"
    needs: [ intg-enable-report, intg-get-report,intg-get-token ]
    with:
      is_intg: true
      delete_all: false
    secrets:
      leanix_host: ${{ secrets.LEANIX_HOST }}
      auth_token: ${{ needs.intg-get-token.outputs.token }}
      encryption_password: ${{ secrets.ENCRYPTION_PASSWORD }}