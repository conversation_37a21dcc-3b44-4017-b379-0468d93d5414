{"name": "ccc-aws-services", "version": "1.1.1-SNAPSHOT", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --mode development", "upload": "tsc && vite build --mode upload", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@leanix/reporting": "^0.4.147", "@types/uuid": "^9.0.8", "antd": "^5.24.9", "moment": "^2.30.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.87.0", "uuid": "^9.0.1", "virtualizedtableforantd4": "^1.3.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@antfu/eslint-config": "^3.16.0", "@eslint-react/eslint-plugin": "^1.48.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "^5.8.3", "vite": "^6.3.4", "vite-plugin-lxr": "^5.0.16"}, "author": "<PERSON><PERSON><PERSON>", "license": "UNLICENSED", "description": "CCC - AWS Services", "leanixReport": {"id": "ch.helvetia.eam.ccc_aws_services", "title": "CCC - AWS Services", "defaultConfig": {}}}