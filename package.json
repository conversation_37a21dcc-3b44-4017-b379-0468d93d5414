{"name": "data-protection", "version": "1.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build --mode development", "upload": "tsc && vite build"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@leanix/reporting": "^0.4.118", "@types/react-highlight-words": "^0.16.4", "antd": "^5.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.18.0", "sass": "^1.55.0", "virtualizedtableforantd4": "^1.2.2", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@types/react": "^18.0.18", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react-refresh": "^1.3.6", "typescript": "^4.8.3", "vite": "^3.1.0", "vite-plugin-lxr": "^0.0.6"}, "author": "<PERSON><PERSON><PERSON>", "description": "Test", "leanixReport": {"id": "ch.helvetia.data_protection", "title": "Data Protection – Data Processing Inventory", "defaultConfig": {}}}