// Import interfaces
import {Phases, DiamondColors} from '../models/data';

const phaseColors: Phases = {
  'null': { 'color': '#FFFFFF', 'label': '' },
  'plan': { 'color': '#d8d8d8', 'label': '' },
  'phaseIn': { 'color': '#aaaaaa', 'label': '' },
  'active': { 'color': '#496a8f', 'label': '' },
  'phaseOut': { 'color': '#ffd02c', 'label': '' },
  'endOfLife': { 'color': '#C00000', 'label': '' }, /* test color, change to Fully transparent with the following 'rgb(0,0,0,0)' */
  '_customGoLiveMilestone1': { 'color': '#496a8f', 'label': '' },
  '_customGoLiveMilestone2': { 'color': '#496a8f', 'label': '' },
  '_customGoLiveMilestone3': { 'color': '#496a8f', 'label': '' },
  '_customGoLiveMilestone4': { 'color': '#496a8f', 'label': '' },
  '_customSwitchOffMilestone1': { 'color': '#496a8f', 'label': '' },
  '_customSwitchOffMilestone2': { 'color': '#496a8f', 'label': '' },
  '_customSwitchOffMilestone3': { 'color': '#496a8f', 'label': '' },
  '_customSwitchOffMilestone4': { 'color': '#496a8f', 'label': '' }
}

export const diamondColors: DiamondColors = {
  '_customGoLiveMilestone1': '#4B8B07',
  '_customGoLiveMilestone2': '#4B8B07',
  '_customGoLiveMilestone3': '#4B8B07',
  '_customGoLiveMilestone4': '#4B8B07',
  '_customSwitchOffMilestone1': '#cc0000',
  '_customSwitchOffMilestone2': '#cc0000',
  '_customSwitchOffMilestone3': '#cc0000',
  '_customSwitchOffMilestone4': '#cc0000'
}

export const roadmapColors = {
  phaseColors: phaseColors,
  diamondColors: diamondColors
}


