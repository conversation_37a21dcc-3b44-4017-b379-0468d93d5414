import type { Application, BaseApplication, Dashboard, ITRessort, Lifecycle, RessortApplication } from '@app/model'
import type { ColumnsType } from 'antd/es/table'
import { InfoCircleOutlined } from '@ant-design/icons'
import ValueTag from '@components/basic/ValueTag'
import CompleteScoreView from '@components/fields/CompleteScoreView'
import DescriptionValue from '@components/fields/DescriptionValue'
import FieldValue from '@components/fields/FieldValue'
import FieldValueRole from '@components/fields/FieldValueRole'
import LifeCycleView from '@components/fields/LifeCycleView'
import MissingTag from '@components/fields/MissingTag'
import ValidationInfo from '@components/fields/ValidationInfo'
import { Popover, Space, Typography } from 'antd'
import * as XLSX from 'xlsx'
import { IT_RESSORT_TAG_GROUP_ID } from './../constants'
import {
  createFilters,
  getApplicationRoles,
  getCompletedScore,
  getCorpUnit,
  getLifecyclePhase,
  getUserGroupAllOwner,
  getUserGroupOwner,
  isHostingCountryRelevant,
  isSaaS,
  parseBusinessCapability,
  parseDomain,
  statColor
} from './../helpers'

const { Text } = Typography

export const ressortOverview: Dashboard = {
  id: 'ressortOverview',
  name: 'CH-Ressort Overview',
  filters: ['textSearch', 'itRessorts', 'corpUnitsSmall', 'unexpandAll'],
  stats: [
    {
      id: 'applications',
      label: 'Applications:',
      value: '',
      suffix: '',
      color: undefined
    },
    {
      id: 'completed',
      label: 'Completed (%):',
      value: '',
      suffix: '%',
      color: undefined
    },
    {
      id: 'missing',
      label: 'Missing Values:',
      value: '',
      suffix: '',
      color: undefined
    }
  ],
  calculateStats: (applications) => {
    let totalMissingFields = 0
    let totCompleted = 0
    let countApps = 0

    applications.forEach((app) => {
      if (app.relevantForCalculations) {
        totalMissingFields += app.missingFields
        totCompleted += app.completed
        countApps++
      }
      if (app.children && app.children.length > 0) {
        app.children.forEach((child) => {
          if (child.relevantForCalculations) {
            totalMissingFields += child.missingFields
            totCompleted += child.completed
            countApps++
          }
        })
      }
    })

    const totalCompleted = totCompleted / (100 * countApps) * 100

    return ressortOverview.stats.map(stat => ({
      ...stat,
      value: stat.id === 'completed' ? `${totalCompleted.toFixed(1)}` : stat.id === 'missing' ? `${totalMissingFields}` : `${countApps}`,
      color: stat.id === 'applications' ? undefined : stat.id === 'completed' ? statColor(totalCompleted) : stat.id === 'missing' ? statColor(totalCompleted) : undefined
    }))
  },

  parse: (appsRaw: any[]) => {
    const validate = (app: RessortApplication) => {
      if (!app.name) {
        app.missingFields++
      }
      // IT Ressort not relevant
      if (!app.sourcing || app.sourcing.length === 0) {
        app.missingFields++
      }
      if (!app.softwareType || app.softwareType.length === 0) {
        app.missingFields++
      }
      if (!app.guiType || app.guiType.length === 0) {
        app.missingFields++
      }
      if (!app.description || app.description.length < 50) {
        app.missingFields++
      }
      if (!app.lifecycle || app.lifecycle.phases.filter(p => p.phase === 'active' || p.phase === 'phaseIn' || p.phase === 'phaseOut').length === 0) {
        app.missingFields++
      }

      if ((!app.hostingProviderCountry || app.hostingProviderCountry.length === 0) && isHostingCountryRelevant(app)) {
        app.missingFields++
      }

      if (isSaaS(app)) {
        if (!app.relApplicationToSoftwareServiceProvider || app.relApplicationToSoftwareServiceProvider.length === 0) {
          app.missingFields++
        }
      }
      else {
        if (!app.relApplicationToHostingProvider || app.relApplicationToHostingProvider.length === 0) {
          app.missingFields++
        }
        if (!app.relApplicationToOperationsProvider || app.relApplicationToOperationsProvider.length === 0) {
          app.missingFields++
        }
        if (!app.relApplicationToSoftwareServiceProvider || app.relApplicationToSoftwareServiceProvider.length === 0) {
          app.missingFields++
        }
      }

      if (!app.appOwnerTechnical || app.appOwnerTechnical.length === 0) {
        app.missingFields++
      }
      if (!app.appOwnerFunctional || app.appOwnerFunctional.length === 0) {
        app.missingFields++
      }
      if (!app.userAccessDataCategory || app.userAccessDataCategory.length === 0) {
        app.missingFields++
      }

      // if (app.name === "AT-ANJA") console.log(app, "TESSST")

      app.completed = getCompletedScore(app.missingFields)
      app.baddestScore = app.completed
    }

    const applications: RessortApplication[] = []

    appsRaw/* .filter(app => !app.name.toLowerCase().includes("(poc)")) */.forEach((app) => {
      const roles = getApplicationRoles(app.subscriptions)

      const itRessort: ITRessort | undefined = app.tags?.filter((tag: any) => tag.tagGroup?.id === IT_RESSORT_TAG_GROUP_ID).length > 0
        ? app.tags?.filter((tag: any) => tag.tagGroup?.id === IT_RESSORT_TAG_GROUP_ID)[0]
        : undefined

      const application: RessortApplication = {
        id: app.id,
        name: app.name,
        level: app.level,
        corpUnit: getCorpUnit(app.name),
        sourcing: lx.translateFieldValue('Application', 'sourcing', app.sourcing),
        softwareType: lx.translateFieldValue('Application', 'softwareType', app.softwareType),
        guiType: app.guiType ? [...app.guiType?.map((t: any) => (lx.translateFieldValue('Application', 'guiType', t)))] : undefined,
        lifecycle: app.lifecycle ? app.lifecycle as Lifecycle : undefined,
        domain: parseDomain(app.relApplicationToDomain),
        businessCapabilities: parseBusinessCapability(app.relApplicationToBusinessCapability),
        appOwnerTechnical: roles.applicationOwnerTechnical,
        appOwnerFunctional: roles.applicationResponsible,
        businessAreaOwner: getUserGroupOwner(app.relApplicationToUserGroup),
        allBusinessAreaOwners: getUserGroupAllOwner(app.relApplicationToUserGroup),
        validBusinessAreaOwner: true,
        description: app.description,
        missingFields: 0,
        itRessort,
        completed: 100,
        baddestScore: 100,
        children: undefined,
        relevantForCalculations: true,
        hostingProviderCountry: app.hostingProviderCountry || [],
        userAccessDataCategory: app.UserAccessDataCategory,
        relApplicationToHostingProvider: app.relApplicationToHostingProvider.edges.map((item: any) => ({
          id: item.node.factSheet.id,
          name: item.node.factSheet.name
        })),
        relApplicationToOperationsProvider: app.relApplicationToOperationsProvider.edges.map((item: any) => ({
          id: item.node.factSheet.id,
          name: item.node.factSheet.name
        })),
        relApplicationToSoftwareServiceProvider: app.relApplicationToSoftwareServiceProvider.edges.map((item: any) => ({
          id: item.node.factSheet.id,
          name: item.node.factSheet.name
        }))
      }

      if (app.relToChild.edges.length > 0) {
        application.children = app.relToChild.edges.filter((child: any) => child.node.factSheet.lifecycle?.asString !== 'endOfLife' /* && !child.node.factSheet.name.toLowerCase().includes("(poc)") */).map((child: any) => {
          const itRessort: ITRessort | undefined = child.node.factSheet.tags?.filter((tag: any) => tag.tagGroup?.id === IT_RESSORT_TAG_GROUP_ID).length > 0
            ? child.node.factSheet.tags?.filter((tag: any) => tag.tagGroup?.id === IT_RESSORT_TAG_GROUP_ID)[0]
            : undefined

          const childApp: RessortApplication = {
            id: child.node.factSheet.id,
            name: child.node.factSheet.name,
            level: child.node.factSheet.level,
            corpUnit: getCorpUnit(child.node.factSheet.name),
            sourcing: lx.translateFieldValue('Application', 'sourcing', child.node.factSheet.sourcing),
            softwareType: lx.translateFieldValue('Application', 'softwareType', child.node.factSheet.softwareType),
            guiType: child.node.factSheet.guiType ? [...child.node.factSheet.guiType?.map((t: any) => (lx.translateFieldValue('Application', 'guiType', t)))] : undefined,
            lifecycle: child.node.factSheet.lifecycle ? child.node.factSheet.lifecycle as Lifecycle : undefined,
            domain: parseDomain(child.node.factSheet.relApplicationToDomain),
            businessCapabilities: parseBusinessCapability(child.node.factSheet.relApplicationToBusinessCapability),
            appOwnerTechnical: getApplicationRoles(child.node.factSheet.subscriptions).applicationOwnerTechnical,
            appOwnerFunctional: getApplicationRoles(child.node.factSheet.subscriptions).applicationResponsible,
            businessAreaOwner: getUserGroupOwner(child.node.factSheet.relApplicationToUserGroup),
            allBusinessAreaOwners: getUserGroupAllOwner(child.node.factSheet.relApplicationToUserGroup),
            validBusinessAreaOwner: true,
            description: child.node.factSheet.description,
            itRessort,
            missingFields: 0,
            completed: 100,
            baddestScore: 100,
            children: undefined,
            relevantForCalculations: true,
            hostingProviderCountry: child.node.factSheet.hostingProviderCountry || [],
            userAccessDataCategory: child.node.factSheet.UserAccessDataCategory,
            relApplicationToHostingProvider: child.node.factSheet.relApplicationToHostingProvider.edges.map((item: any) => ({
              id: item.node.factSheet.id,
              name: item.node.factSheet.name
            })),
            relApplicationToOperationsProvider: child.node.factSheet.relApplicationToOperationsProvider.edges.map((item: any) => ({
              id: item.node.factSheet.id,
              name: item.node.factSheet.name
            })),
            relApplicationToSoftwareServiceProvider: child.node.factSheet.relApplicationToSoftwareServiceProvider.edges.map((item: any) => ({
              id: item.node.factSheet.id,
              name: item.node.factSheet.name
            }))
          }

          validate(childApp)
          return childApp
        })
      }

      validate(application)

      applications.push(application)
    })

    return applications.sort((a, b) => a.name.localeCompare(b.name))
  },

  getColumns: (apps, sortedInfo, filteredInfo, searchText, highlightText) => {
    const columns: ColumnsType<BaseApplication> = [
      {
        title: 'C.',
        width: 90,
        dataIndex: 'completed',
        key: 'completed',
        render: (_text: string, record) => <CompleteScoreView app={record} />,
        filters: createFilters('completed', apps),
        filteredValue: filteredInfo.completed || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.completed : record.completed === value
          const isChildMatch = record.children?.some(child =>
            value === 'no_value' ? !child.completed : child.completed === value
          )
          return (isMainMatch || isChildMatch) || false
        },
        sorter: (a, b) => a.baddestScore - b.baddestScore,
        sortOrder: sortedInfo.columnKey === 'completed' ? sortedInfo.order : null
      },
      {
        title: 'Name',
        width: 300,
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record) => (
          <a
            dangerouslySetInnerHTML={{ __html: highlightText(text, searchText) }}
            onClick={() => lx.openLink(`/factsheet/Application/${record.id}`, '_blank')}
          >
          </a>
        ),
        sorter: (a, b) => {
          const aName = a.name.toLowerCase()
          const bName = b.name.toLowerCase()
          if (aName !== bName) {
            return aName.localeCompare(bName)
          }
          if (a.children && b.children) {
            const aChildName = a.children.map(child => child.name.toLowerCase()).join(',')
            const bChildName = b.children.map(child => child.name.toLowerCase()).join(',')
            return aChildName.localeCompare(bChildName)
          }
          return 0
        },
        sortOrder: sortedInfo.columnKey === 'name' ? sortedInfo.order : null
      },
      {
        title: 'IT Ressort',
        dataIndex: 'itRessort',
        key: 'itRessort',
        width: 90,
        render: (_text: any, record) => {
          const app = record as RessortApplication
          return app.itRessort
            ? (
                <span className="lxTag" style={{ backgroundColor: app.itRessort.color }}>
                  {app.itRessort.name}
                </span>
              )
            : null
        }

      },
      {
        title: 'Sourcing',
        dataIndex: 'sourcing',
        key: 'sourcing',
        width: 90,
        ellipsis: { showTitle: false },
        render: (value: any) => <FieldValue value={value} />,
        sorter: (a, b) =>
          (a.sourcing || '').toLowerCase().localeCompare((b.sourcing || '').toLowerCase()),
        filters: createFilters('sourcing', apps),
        filteredValue: filteredInfo.sourcing || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.sourcing : record.sourcing === value
          const isChildMatch = record.children?.some(child =>
            value === 'no_value' ? !child.sourcing : child.sourcing === value
          )
          return (isMainMatch || isChildMatch) || false
        },
        sortOrder: sortedInfo.columnKey === 'sourcing' ? sortedInfo.order : null
      },
      {
        title: 'Software Type',
        dataIndex: 'softwareType',
        key: 'softwareType',
        width: 90,
        ellipsis: { showTitle: false },
        render: (value: any) => <FieldValue value={value} />,
        sorter: (a, b) =>
          (a.softwareType || '').toLowerCase().localeCompare((b.softwareType || '').toLowerCase()),
        filters: createFilters('softwareType', apps),
        filteredValue: filteredInfo.softwareType || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.softwareType : record.softwareType === value
          const isChildMatch = record.children?.some(child =>
            value === 'no_value' ? !child.softwareType : child.softwareType === value
          )
          return (isMainMatch || isChildMatch) || false
        },
        sortOrder: sortedInfo.columnKey === 'softwareType' ? sortedInfo.order : null
      },
      {
        title: 'GUI Type',
        dataIndex: 'guiType',
        key: 'guiType',
        width: 90,
        ellipsis: { showTitle: false },
        render: (value: any) => <FieldValue limit={4} value={value} />,
        sorter: (a, b) => (a.guiType?.length || 0) - (b.guiType?.length || 0),
        filters: createFilters('guiType', apps),
        filteredValue: filteredInfo.guiType || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.guiType : record.guiType?.includes(value)
          const isChildMatch = record.children?.some(child =>
            value === 'no_value' ? !child.guiType : child.guiType?.includes(value)
          )
          return (isMainMatch || isChildMatch) || false
        },
        sortOrder: sortedInfo.columnKey === 'guiType' ? sortedInfo.order : null
      },
      {
        title: (
          <Space>
            <Text>Descr.</Text>
            <ValidationInfo text="Description is required and should be at least 50 characters long." />
          </Space>
        ),
        dataIndex: 'description',
        width: 90,
        key: 'description',
        ellipsis: { showTitle: false },
        render: (value: any) => <DescriptionValue value={value} />,
        sorter: (a, b) =>
          (a.description || '').toLowerCase().localeCompare((b.description || '').toLowerCase()),
        sortOrder: sortedInfo.columnKey === 'description' ? sortedInfo.order : null
      },
      {
        title: 'Lifecycle',
        key: 'lifecycle',
        width: 100,
        ellipsis: { showTitle: false },
        render: (_value: any, record) => <LifeCycleView application={record} />,
        sorter: (a, b) =>
          (a.lifecycle?.phases.length || 0) - (b.lifecycle?.phases.length || 0),
        sortOrder: sortedInfo.columnKey === 'lifecycle' ? sortedInfo.order : null
      },
      {
        title: (
          <Space>
            <Text>Provider</Text>
            <Popover
              content={(
                <div style={{ maxWidth: '300px' }}>
                  <Text strong>Important Score Requirements</Text>
                  <ul style={{ paddingLeft: '16px', margin: '0' }}>
                    <li>
                      <Text strong>Hosting Country:</Text>
                      {' '}
                      Only mandatory when a hosting provider is
                      specified (Critical for score calculation)
                    </li>
                    <li style={{ marginTop: '8px' }}>
                      <Text strong>SaaS Applications:</Text>
                      {' '}
                      A Software/Service Provider entry alone is
                      sufficient (Critical for score calculation)
                    </li>
                    <li style={{ marginTop: '8px' }}>
                      <Text strong>Other Cases:</Text>
                      {' '}
                      Both Hosting Provider and Operations Provider
                      should be maintained (Critical for score calculation)
                    </li>
                  </ul>
                </div>
              )}
              title="Validation"
            >
              <InfoCircleOutlined />
            </Popover>

          </Space>
        ),
        dataIndex: 'providerDetails',
        key: 'providerDetails',
        children: [
          {
            title: (
              <Space>
                <Text>Hosting Country</Text>
              </Space>
            ),
            dataIndex: 'hostingProviderCountry',
            key: 'hostingProviderCountry',
            width: 144,
            ellipsis: { showTitle: false },
            render: (_value: any, application) => {
              const app = application as RessortApplication

              if (app.hostingProviderCountry && app.hostingProviderCountry.length > 0) {
                return (
                  <Space direction="vertical">
                    {app.hostingProviderCountry.map(p => (
                      <ValueTag
                        limit={17}
                        key={p}
                        text={lx.translateFieldValue('Application', 'hostingProviderCountry', p)}
                      />
                    ))}
                  </Space>
                )
              }
              else {
                return (<MissingTag isRelevant={isHostingCountryRelevant(app)} />)
              }
            },
            filters: createFilters('hostingProviderCountry', apps),
            filteredValue: filteredInfo.hostingProviderCountry || null,
            onFilter: (value: any, record) => {
              const app = record as RessortApplication
              const childs = record.children as RessortApplication[]
              const isMainMatch
                                = value === 'no_value'
                                  ? app.hostingProviderCountry?.length === 0
                                  : app.hostingProviderCountry?.includes(value)
              const isChildMatch = childs?.some(child =>
                value === 'no_value'
                  ? child.hostingProviderCountry?.length === 0
                  : child.hostingProviderCountry?.includes(value)
              )
              return (isMainMatch || isChildMatch) || false
            }
          },
          {
            title: (
              <Space>
                <Text>Hosting</Text>
              </Space>
            ),
            dataIndex: 'relApplicationToHostingProvider',
            key: 'relApplicationToHostingProvider',
            width: 144,
            ellipsis: { showTitle: false },
            render: (_value: any, record) => {
              const app = record as RessortApplication
              if (app.relApplicationToHostingProvider && app.relApplicationToHostingProvider.length > 0) {
                return (
                  <Space direction="vertical">
                    {app.relApplicationToHostingProvider.map(p => (
                      <ValueTag limit={17} key={p.id} text={p.name} />
                    ))}
                  </Space>
                )
              }
              else {
                return (<MissingTag isRelevant={!isSaaS(app)} />)
              }
            },
            filters: createFilters('relApplicationToHostingProvider', apps),
            filteredValue: filteredInfo.relApplicationToHostingProvider || null,
            onFilter: (value: any, record) => {
              const app = record as RessortApplication
              const childs = record.children as RessortApplication[]
              const isMainMatch
                                = value === 'no_value'
                                  ? !app.relApplicationToHostingProvider
                                  || app.relApplicationToHostingProvider.length === 0
                                  : app.relApplicationToHostingProvider
                                    && app.relApplicationToHostingProvider.some((item: any) => item.id === value)
              const isChildMatch = childs?.some(child =>
                value === 'no_value'
                  ? !child.relApplicationToHostingProvider
                  || child.relApplicationToHostingProvider.length === 0
                  : child.relApplicationToHostingProvider
                    && child.relApplicationToHostingProvider.some((item: any) => item.id === value)
              )
              return isMainMatch || isChildMatch
            }
          },
          {
            title: (
              <Space>
                <Text>Operations</Text>
              </Space>
            ),
            dataIndex: 'relApplicationToOperationsProvider',
            key: 'relApplicationToOperationsProvider',
            width: 144,
            ellipsis: { showTitle: false },
            render: (_value: any, record) => {
              const app = record as RessortApplication

              if (app.relApplicationToOperationsProvider && app.relApplicationToOperationsProvider.length > 0) {
                return (
                  <Space direction="vertical">
                    {app.relApplicationToOperationsProvider.map(p => (
                      <ValueTag limit={17} key={p.id} text={p.name} />
                    ))}
                  </Space>
                )
              }
              else {
                return (<MissingTag isRelevant={!isSaaS(app)} />)
              }
            },
            filters: createFilters('relApplicationToOperationsProvider', apps),
            filteredValue: filteredInfo.relApplicationToOperationsProvider || null,
            onFilter: (value: any, record: Application) => {
              const app = record as RessortApplication
              const childs = record.children as RessortApplication[]
              const isMainMatch
                                = value === 'no_value'
                                  ? !app.relApplicationToOperationsProvider
                                  || app.relApplicationToOperationsProvider.length === 0
                                  : app.relApplicationToOperationsProvider
                                    && app.relApplicationToOperationsProvider.some((item: any) => item.id === value)
              const isChildMatch = childs?.some(child =>
                value === 'no_value'
                  ? !child.relApplicationToOperationsProvider
                  || child.relApplicationToOperationsProvider.length === 0
                  : child.relApplicationToOperationsProvider
                    && child.relApplicationToOperationsProvider.some((item: any) => item.id === value)
              )
              return isMainMatch || isChildMatch
            }
          },
          {
            title: (
              <Space>
                <Text>Software/Service</Text>
              </Space>
            ),
            dataIndex: 'relApplicationToSoftwareServiceProvider',
            key: 'relApplicationToSoftwareServiceProvider',
            ellipsis: { showTitle: false },
            width: 144,
            render: (_value: any, application) => {
              const app = application as RessortApplication
              if (app.relApplicationToSoftwareServiceProvider && app.relApplicationToSoftwareServiceProvider.length > 0) {
                return (
                  <Space direction="vertical">
                    {app.relApplicationToSoftwareServiceProvider.map(p => (
                      <ValueTag limit={17} key={p.id} text={p.name} />
                    ))}
                  </Space>
                )
              }
              else {
                return (<MissingTag />)
              }
            },
            filters: createFilters('relApplicationToSoftwareServiceProvider', apps),
            filteredValue: filteredInfo.relApplicationToSoftwareServiceProvider || null,
            onFilter: (value: any, record: Application) => {
              const app = record as RessortApplication
              const childs = record.children as RessortApplication[]
              const isMainMatch
                                = value === 'no_value'
                                  ? !app.relApplicationToSoftwareServiceProvider
                                  || app.relApplicationToSoftwareServiceProvider.length === 0
                                  : app.relApplicationToSoftwareServiceProvider
                                    && app.relApplicationToSoftwareServiceProvider.some((item: any) => item.id === value)
              const isChildMatch = childs?.some(child =>
                value === 'no_value'
                  ? !child.relApplicationToSoftwareServiceProvider
                  || child.relApplicationToSoftwareServiceProvider.length === 0
                  : child.relApplicationToSoftwareServiceProvider
                    && child.relApplicationToSoftwareServiceProvider.some((item: any) => item.id === value)
              )
              return isMainMatch || isChildMatch
            }
          }
        ]
      },
      {
        title: (
          <Space>
            <Text>User Group Cat.</Text>
          </Space>
        ),
        dataIndex: 'userAccessDataCategory',
        key: 'userAccessDataCategory',
        width: 160,
        ellipsis: { showTitle: false },
        render: (_value: any, application: Application) => {
          const app = application as RessortApplication
          if (app.userAccessDataCategory && app.userAccessDataCategory.length > 0) {
            return (
              <Space direction="vertical">
                {app.userAccessDataCategory.map(p => (
                  <ValueTag
                    key={p}
                    text={lx.translateFieldValue('Application', 'UserAccessDataCategory', p)}
                  />
                ))}
              </Space>
            )
          }
          else {
            return (<MissingTag />)
          }
        },
        filters: createFilters('userAccessDataCategory', apps),
        filteredValue: filteredInfo.userAccessDataCategory || null,
        onFilter: (value: any, record: Application) => {
          const app = record as RessortApplication
          const childs = record.children as RessortApplication[]
          const isMainMatch
                        = value === 'no_value'
                          ? !app.userAccessDataCategory || app.userAccessDataCategory.length === 0
                          : app.userAccessDataCategory && app.userAccessDataCategory.includes(value)
          const isChildMatch = childs?.some(child =>
            value === 'no_value'
              ? !child.userAccessDataCategory || child.userAccessDataCategory.length === 0
              : child.userAccessDataCategory && child.userAccessDataCategory.includes(value)
          )
          return isMainMatch || isChildMatch
        }
      },
      {
        title: 'Responsible Application Owner (technical)',
        dataIndex: 'appOwnerTechnical',
        key: 'appOwnerTechnical',
        width: 90,
        ellipsis: { showTitle: false },
        render: (_value: any, record) => (<FieldValueRole value={record.appOwnerTechnical} />),
        sorter: (a, b) => (a.appOwnerTechnical?.length || 0) - (b.appOwnerTechnical?.length || 0),
        filters: createFilters('appOwnerTechnical', apps),
        filteredValue: filteredInfo.appOwnerTechnical || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? record.appOwnerTechnical.length === 0 : record.appOwnerTechnical?.includes(value)
          const isChildMatch = record.children?.some(child => value === 'no_value' ? child.appOwnerTechnical.length === 0 : child.appOwnerTechnical?.includes(value))
          return (isMainMatch || isChildMatch) || false
        },
        sortOrder: sortedInfo.columnKey === 'appOwnerTechnical' ? sortedInfo.order : null
      },
      {
        title: 'Responsible Application Owner (functional)',
        dataIndex: 'appOwnerFunctional',
        key: 'appOwnerFunctional',
        width: 90,
        ellipsis: { showTitle: false },
        render: (_value: any, record: Application) => (<FieldValueRole value={record.appOwnerFunctional} />),
        sorter: (a: Application, b: Application) => (a.appOwnerFunctional?.length || 0) - (b.appOwnerFunctional?.length || 0),
        filters: createFilters('appOwnerFunctional', apps),
        filteredValue: filteredInfo.appOwnerFunctional || null,
        onFilter: (value: any, record: Application) => {
          const isMainMatch = value === 'no_value' ? record.appOwnerFunctional.length === 0 : record.appOwnerFunctional?.includes(value)
          const isChildMatch = record.children?.some(child => value === 'no_value' ? child.appOwnerFunctional.length === 0 : child.appOwnerFunctional?.includes(value))
          return (isMainMatch || isChildMatch) || false
        },
        sortOrder: sortedInfo.columnKey === 'appOwnerFunctional' ? sortedInfo.order : null
      }
    ]
    return columns
  },

  export: (excel, applications) => {
    const prepareData = (apps: RessortApplication[]) => {
      const createRow = (app: RessortApplication) => ({
        'Completed': app.completed,
        'ID': app.id,
        'Name': app.name,
        'IT Ressort': app.itRessort ? app.itRessort.name : '',
        'Level': app.level,
        'Sourcing': app.sourcing ? app.sourcing : '',
        'Software Type': app.softwareType ? app.softwareType : '',
        'GUI Type': app.guiType?.join('; '),
        'Description': app.description ? app.description : '',

        'Lifecycle: Phase In': getLifecyclePhase('phaseIn', app.lifecycle) || '',
        'Lifecycle: Go-Live': getLifecyclePhase('active', app.lifecycle) || '',
        'Lifecycle: Phase Out': getLifecyclePhase('phaseOut', app.lifecycle) || '',

        'Hosting Provider Country': (app.hostingProviderCountry || []).join('; '),
        'Hosting Provider': (app.relApplicationToHostingProvider.map(p => (p.name)) || []).join('; '),
        'Operations Provider': (app.relApplicationToOperationsProvider.map(p => (p.name)) || []).join('; '),
        'Software/Service Provider': (app.relApplicationToSoftwareServiceProvider.map(p => (p.name)) || []).join('; '),

        'Application Owner (technical)': (app.appOwnerTechnical || []).join('; '),
        'Application Owner (functional)': (app.appOwnerFunctional || []).join('; '),
        'User Group Categories': (app.userAccessDataCategory || []).join('; ')
      })

      const parsed: any[] = []

      apps.forEach((app) => {
        if (app.relevantForCalculations) {
          parsed.push(createRow(app))
        }

        if (app.children && app.children.length > 0) {
          app.children.forEach((child) => {
            if (child.relevantForCalculations) {
              parsed.push(createRow(child as RessortApplication))
            }
          })
        }
      })
      return parsed
    }

    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(
      prepareData(applications as RessortApplication[])
    )

    XLSX.utils.book_append_sheet(wb, ws_application, 'Data Quality')
    XLSX.writeFile(wb, excel ? 'export.xlsx' : 'export.csv')
  }
}
