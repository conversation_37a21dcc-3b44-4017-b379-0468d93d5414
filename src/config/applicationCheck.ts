import type { FormInstance } from 'antd'
import type { Report<PERSON>ser, WizardField, WizardFormModel, WizardStep } from '../model'
import dayjs from 'dayjs'
import { getFieldByName } from '../helpers'
import { useLeaniX } from '../hooks/leanix'
import { handleExternalApps, lifecycleOrderControl } from './helpers'

const { getApplicationDetails, saveChangesApplication } = useLeaniX()

const initCheck = async (form: FormInstance, appId: string, appDetails:any) => {
  console.log('INIT CHECK', appId)
  if (appDetails) {
    let external = false


    // step 0
    form.setFieldValue('appId', appDetails.id)

    let name = appDetails.name

    if (name.includes('(ext.)')) {
      form.setFieldValue('external', true)
      name = name.replaceAll('(ext.)', '').trim()
      external = true
    }
    else {
      form.setFieldValue('external', false)
    }

    const nameParts = name.split('-')
    if (nameParts.length > 1) {
      form.setFieldValue('prefix', `${name.split('-')[0]}-`)
      form.setFieldValue('name', name.split('-').slice(1).join('-'))
    }
    else {
      form.setFieldValue('name', name)
    }

    if (external) {
      form.setFieldValue('sourcing', 'none')
      form.setFieldValue('softwareType', 'notApplicable')
      form.setFieldValue('plattform', 'na')
    }
    else {
      form.setFieldValue('sourcing', appDetails.sourcing)
      form.setFieldValue('softwareType', appDetails.softwareType)
      form.setFieldValue('plattform', appDetails.plattform)
    }

    form.setFieldValue('description', appDetails.description)
    form.setFieldValue('saaSClassification', appDetails.saaSClassification)
    form.setFieldValue('saaSClassificationComment', appDetails.saaSClassificationComment)
    form.setFieldValue('hostingProviderCountry', appDetails.hostingProviderCountry)
    form.setFieldValue('relApplicationToHostingProvider', [...appDetails.relApplicationToHostingProvider.map((r:any) => (r.factSheetId))])
    form.setFieldValue('relApplicationToOperationsProvider', [...appDetails.relApplicationToOperationsProvider.map((r:any) => (r.factSheetId))])
    form.setFieldValue('relApplicationToSoftwareServiceProvider', [...appDetails.relApplicationToSoftwareServiceProvider.map((r:any) => (r.factSheetId))])

    form.setFieldValue('guiType', appDetails.guiType)
    form.setFieldValue('url', appDetails.url)

    // step 1
    form.setFieldValue('phaseIn', appDetails.phaseIn ? dayjs(appDetails.phaseIn) : undefined)
    form.setFieldValue('active', appDetails.active ? dayjs(appDetails.active) : undefined)
    form.setFieldValue('phaseOut', appDetails.phaseOut ? dayjs(appDetails.phaseOut) : undefined)

    // application domain & capabilities
    form.setFieldValue('applicationDomain', appDetails.applicationDomain)
    form.setFieldValue('businessCapabilities', appDetails.businessCapabilities)

    // business area
    form.setFieldValue('businessAreaOwner', appDetails.businessAreaOwner)
    form.setFieldValue('businessAreaUsers', appDetails.businessAreaUsers)

    // new
    form.setFieldValue('businessAreas', appDetails.businessAreaParsed)

    // user admin
    form.setFieldValue('userAdmin', appDetails.userAdmin)
    form.setFieldValue('commentOnUserAdmin', appDetails.commentOnUserAdmin)
    form.setFieldValue('userAccessControl', appDetails.userAccessControl)
    form.setFieldValue('commentOnUserAccessControl', appDetails.commentOnUserAccessControl)
    form.setFieldValue('reachableFromNonManagedDevice', appDetails.reachableFromNonManagedDevice)
    form.setFieldValue('multiFactorAuthentication', appDetails.multiFactorAuthentication)
    form.setFieldValue('multiFactorAuthenticationComment', appDetails.multiFactorAuthenticationComment)
    // form.setFieldValue("needsMFA", appDetails.needsMFA);

    form.setFieldValue('applicationOwnerTechnical', appDetails.applicationOwnerTechnical)
    form.setFieldValue('applicationResponsibleFunctional', appDetails.applicationResponsibleFunctional)
  }
  else {
    console.log('no app id selected')
  }
}

export const checkApplicationForm: WizardFormModel = {
  id: 'checkApplication',
  name: 'Application Check',
  originFactSheet: 'Application',
  addWithTemporaryMode: false,
  onlyAdmin: false,
  saveErrorMessage: 'Error while updating the application. Please try again',
  saveSuccessMessage: 'Application successfully updatet',
  steps: [
    {
      name: 'Select Application',
      rules: [
        async (form, steps, currentStep, currentUser, update) => {
          const appId: string | undefined = form.getFieldValue('selectedApplication')

          if (appId && appId) {
            // console.log("Selected Application: ", appId);
            const appDetails = await getApplicationDetails(appId)
            console.log('APP DETAILS: ', appDetails)

            await initCheck(form, appId, appDetails)

            console.log(currentUser)
          }
        }
      ],
      fields: [
        {
          title: 'Select Application',
          name: 'selectedApplication',
          fieldType: 'ApplicationSelect',
          loadFactSheet: 'Application',
          description: 'Which Application do you want to select?',
          required: true,
          visible: true
        }

      ],
      customChecks: async (form: FormInstance, currentUser: ReportUser, step: WizardStep) => {

        const appId : string | undefined = form.getFieldValue('selectedApplication');

        if(appId){
          const appDetails = await getApplicationDetails(appId);

          const allowedPeople = [...appDetails.applicationOwnerTechnical, ...appDetails.applicationResponsibleFunctional];

          let allowed = false;
          let responsible = allowedPeople.includes(currentUser.id)

          if(currentUser.admin || currentUser.member || responsible){
            allowed = true

            if(responsible) form.setFieldValue("responsibleAccess",true);
          }

          if(!allowed) return "To run the application check, you must be a LeanIX Member, Admin or EAM or be registered as an Application Owner / Application Responsible for the application"


        }


        return undefined
      }
    },
    {
      name: 'Information',
      rules: [
        // WHEN Sourcing == saas -> add Fields
        (form, steps, currentStep, currentUser, update) => {
          const value: string | undefined = form.getFieldValue('sourcing')
          if (value) {
            const newSteps: WizardStep[] = [...steps]
            const fieldChange1 = getFieldByName(newSteps, currentStep, 'saaSClassification')
            const fieldChange2 = getFieldByName(newSteps, currentStep, 'saaSClassificationComment')
            fieldChange1.visible = value == 'saas'
            fieldChange2.visible = value == 'saas'

            newSteps[currentStep].fields = newSteps[currentStep].fields.map(f =>
              f.name === 'saaSClassification' ? fieldChange1 : f.name === 'saaSClassificationComment' ? fieldChange2 : f
            )
            update(newSteps)
          }
        },
        // WHEN softwaretype === thirdParty ->make software/service providers field required
        (form, steps, currentStep, currentUser, update) => {
          const value: string | undefined = form.getFieldValue('softwareType')
          if (value) {
            const newSteps: WizardStep[] = [...steps]
            const fieldChange = getFieldByName(newSteps, 2, 'relApplicationToSoftwareServiceProvider')
            fieldChange.required = value === 'thirdParty'
            newSteps[2].fields = newSteps[2].fields.map(f => f.name === 'relApplicationToSoftwareServiceProvider' ? fieldChange : f)
            update(newSteps)
          }
        },
        // When Prefix != CH User Admin & Access Control not required
        (form, steps, currentStep, currentUser, update) => {
          const value: string | undefined = form.getFieldValue('prefix')
          // if (value) {
          const newSteps: WizardStep[] = [...steps]
          const fieldsToUpdate = ['userAdmin', 'userAccessControl', 'reachableFromNonManagedDevice', 'multiFactorAuthentication']

          const fields: WizardField[] = fieldsToUpdate.map((fieldToUpdate) => {
            const field = getFieldByName(newSteps, 7, fieldToUpdate)
            field.required = (value === 'CH-')
            return field
          })

          newSteps[7].fields = [...newSteps[7].fields.map((f) => {
            if (fieldsToUpdate.includes(f.name)) {
              return fields.filter(newField => newField.name === f.name)[0]
            }
            else {
              return f
            }
          })]
          update(newSteps)
        },
        // External Application
        (form, steps, currentStep, currentUser, update) => {
          handleExternalApps(form, steps, update, true)
        },
        (form, steps, currentStep, currentUser, update) => {

        const isResponsible = form.getFieldValue("responsibleAccess");

        console.log("USER IS RESPONSIBLE;", isResponsible)

          // Make fields disabled
          if(!currentUser.admin && isResponsible){

            const fieldsToDisable = ['plattform', 'sourcing', 'softwareType']
            const newSteps: WizardStep[] = [...steps]





          }


        }
      ],
      fields: [
        {
          title: 'AppID',
          name: 'appId',
          fieldType: 'Hidden',
          description: 'app id for updating',
          required: true,
          visible: false
        },
        {
          title: 'External Application',
          name: 'external',
          fieldType: 'Switch',
          description: `Placeholder for external partners or partner applications to depict interfaces 
                    between Helvetia systems and these external applications. They normally do not hold Helvetia 
                    data and are not actively used by Helvetia employees. If software licenses or SaaS subscriptions 
                    are acquired, it is generally not considered an external partner application.`,
          required: true,
          visible: true,
          disabled: true
        },
        {
          title: 'Prefix',
          name: 'prefix',
          fieldType: 'Hidden',
          description: 'just the prefix',
          required: false,
          visible: false
        },
        {
          title: 'Name',
          name: 'name',
          fieldType: 'NameField',
          description: 'The name is used to identify this Fact Sheet in the Inventory, Reporting and Search.',
          required: false,
          visible: true,
          disabled: true
        },
        {
          title: 'Description',
          name: 'description',
          fieldType: 'TextArea',
          description: 'Please provide a meaningful description to enable other users to understand the main purpose.',
          required: true,
          visible: true
        },
        {
          title: 'Platform',
          name: 'plattform',
          fieldType: 'SingleSelect',
          description: 'Used to indicate the logical Helvetia platform that an Application or IT Component belongs to.',
          required: false,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'Sourcing',
          name: 'sourcing',
          fieldType: 'SingleSelect',
          description: 'Type of internal, public cloud or external hosting.',
          required: true,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'Saas Classification',
          name: 'saaSClassification',
          fieldType: 'SingleSelect',
          description: 'Classification based on SaaS Framework (Individual = no integration, cost < 2.5k, only public & internal data;'
            + ' Common = User < 100, cost < 200k p.a.a, no FINMA relevant data; Strategic = User > 100, cost > 200k p.a. o., or FINMA relevant data)',
          required: false,
          loadLeanIXOptions: true,
          visible: false
        },
        {
          title: 'SaaS Classification Comment',
          name: 'saaSClassificationComment',
          fieldType: 'TextArea',
          description: 'Any additional comments',
          required: false,
          visible: false
        },

        {
          title: 'Software Type',
          name: 'softwareType',
          fieldType: 'SingleSelect',
          description: '3rd Party Software and Individual Software.',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          title: 'Provider',
          name: 'providerName',
          fieldType: 'AutoCompleteField',
          description: 'Name of Provider',
          required: false,
          loadLeanIXOptions: false,
          loadFactSheet: 'Provider',
          visible: false
        },
        {
          title: 'GUI Types',
          name: 'guiType',
          fieldType: 'MultiSelect',
          description: '',
          required: true,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'URL',
          name: 'url',
          fieldType: 'Text',
          description: 'URL to access the application.',
          required: false,
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Provider',
      rules: [],
      fields: [
        {
          name: 'hostingProviderCountry',
          title: 'Hosting Provider Country',
          description: '',
          required: false,
          fieldType: 'MultiSelect',
          loadLeanIXOptions: true,
          visible: true
        },
        {
          name: 'relApplicationToHostingProvider',
          title: 'Hosting Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        },
        {
          name: 'relApplicationToOperationsProvider',
          title: 'Operations Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        },
        {
          name: 'relApplicationToSoftwareServiceProvider',
          title: 'Software/Service Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Lifecycle',
      rules: [],
      fields: [
        {
          name: 'phaseIn',
          title: 'Phase in',
          description: 'Date since when the Application is in the phase of being built or acquired',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'active',
          title: 'Go-Live',
          description: 'Date since when the Desktop Software is productive and in use',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'phaseOut',
          title: 'Switch Off',
          description: 'Date since when the Application is in the phase of being retired',
          fieldType: 'DatePicker',
          required: false,
          visible: true
        }
      ],
      customChecks: async (form) => {
        const phaseIn = form.getFieldValue('phaseIn')
        const active = form.getFieldValue('active')
        const phaseOut = form.getFieldValue('phaseOut')

        return lifecycleOrderControl(phaseIn, active, phaseOut)
      }
    },
    {
      name: 'Application Domain',
      rules: [],
      fields: [
        {
          name: 'applicationDomain',
          title: 'Application Domain',
          description: 'Which domain is the application assigned to?',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadFactSheet: 'Domain'
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Business Capabilities',
      rules: [],
      fields: [
        {
          name: 'businessCapabilities',
          title: 'Business Capabilities',
          description: 'Which Business Capabilities are supported by this Application?',
          fieldType: 'MultiSelect',
          required: false,
          visible: true,
          loadFactSheet: 'BusinessCapability'
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Business Area',
      rules: [],
      fields: [
        {
          name: 'businessAreas',
          title: 'Business Areas',
          description: 'Please choose whether this Business Area is owning or using this Application (only one owner allowed)',
          fieldType: 'BusinessAreaSelection',
          required: true,
          visible: true,
          loadFactSheet: 'UserGroup'
        }
      ],
      customChecks: async (form: FormInstance, currentUser: ReportUser, step: WizardStep) => {
        return undefined
      }
    },
    {
      name: 'User Administration',
      rules: [],
      fields: [
        {
          name: 'userAdmin',
          title: 'User Administration',
          description: 'Specification of the user administration mechanism',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'commentOnUserAdmin',
          title: 'Comment on User Administration',
          description: 'Optional comment on user administration',
          fieldType: 'TextArea',
          required: false,
          visible: true
        },
        {
          name: 'userAccessControl',
          title: 'User Access Control',
          description: 'Specification of the access control mechanism',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'commentOnUserAccessControl',
          title: 'Comment on User Access Control',
          description: 'Optional comment on user access control',
          fieldType: 'TextArea',
          required: false,
          visible: true
        },
        {
          name: 'reachableFromNonManagedDevice',
          title: 'Application is reachable from a non-helvetia managed device',
          description: 'Indication whether the application is reachable from a non-helvetia managed device',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'multiFactorAuthentication',
          title: 'Multi-Factor Authentication',
          description: 'Indication whether the application has Multi-Factor implemented',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'multiFactorAuthenticationComment',
          title: 'Comment for used MFA type',
          description: 'Optional Comment on Multi-Factor Authentication',
          fieldType: 'TextArea',
          required: false,
          visible: true
        }
        /* {
                    name: "needsMFA",
                    title: "Application needs to implement multi-factor authentication",
                    description: "Indication whether the application needs multi-factor authentication (used for specific use-cases e.g. internal and external access with different authentication type)",
                    fieldType: "SingleSelect",
                    required: true,
                    visible: true,
                    loadLeanIXOptions: true,
                } */
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Subscriptions',
      rules: [],
      fields: [{
        name: 'applicationOwnerTechnical',
        title: 'Application Owner (technical)',
        description: 'The Application Owner (technical) is responsible for maintenance, further development rsp. configuration as well as for the 2nd level support of the relevant application. He ensures that '
          + 'the business and technical requirements are implemented and that a reliable and costefficient '
          + 'operation is ensured in accordance with the business requirements and agreements.',
        fieldType: 'UserSelectMultiple',
        required: true,
        visible: true
      }, {
        name: 'applicationResponsibleFunctional',
        title: 'Application Responsible (functional)',
        description: 'The Application Responsible (functional) assumes functional responsibility for the application. '
          + 'This includes the definition and prioritization of requirements for the further development '
          + 'and improvements of the application. The definition of the security requirements, the rules '
          + 'for assigning access authorizations, and the assessment of business criticality are also his '
          + 'responsibility.',
        fieldType: 'UserSelectMultiple',
        required: true,
        visible: true
      }],
      customChecks: async () => {
        return undefined
      }
    }
  ],
  init: async (form, currentUser, transferData, factSheetId, validate) => {
    // console.log("init application check with ", factSheetId);
    form.setFieldValue('selectedApplication', factSheetId)

    const appDetails = await getApplicationDetails(factSheetId!);

    initCheck(form, factSheetId!,appDetails).then()
    if (validate && factSheetId) { validate().then() }
  },
  save: async (fsData, currentUser, docId) => {
    // console.log("SAVE", fsData, currentUser)
    const appDetails = await getApplicationDetails(fsData.appId)
    console.log('APP DETAILS', appDetails)
    const updated = await saveChangesApplication(fsData.appId, fsData, appDetails)
    console.log('APP UPDATET', updated)
    return fsData.appId
  }
}
