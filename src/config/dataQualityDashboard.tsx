import type {
  Application,
  BaseApplication,
  Dashboard,
  DataQualityApplication,
  Lifecycle
} from '@app/model'
import type { ColumnsType } from 'antd/es/table'
import type { Key } from 'react'
import BusinessAreaOwnerField from '@components/fields/BusinessAreaOwnerField'
import CompleteScoreView from '@components/fields/CompleteScoreView'
import DescriptionValue from '@components/fields/DescriptionValue'
import FieldValue from '@components/fields/FieldValue'
import FieldValueRole from '@components/fields/FieldValueRole'
import L3Field from '@components/fields/L3Field'
import LifeCycleView from '@components/fields/LifeCycleView'
import ValidationInfo from '@components/fields/ValidationInfo'
import WizardLink from '@components/fields/WizardLink'
import { Space, Typography } from 'antd'
import React from 'react'
import * as XLSX from 'xlsx'
import {
  createFilters,
  getApplicationRoles,
  getCorpUnit,
  getLifecyclePhase,
  getUserGroupAllOwner,
  getUserGroupOwner,
  parseBusinessCapability,
  parseDomain,
  statColor,
  validateDataQuality
} from './../helpers'

const { Text } = Typography

export const dataQualityDashboard: Dashboard = {
  id: 'dataQuality',
  name: 'Group Data-Quality Overview',
  filters: ['textSearch', 'corpUnits', 'unexpandAll'],
  stats: [
    {
      id: 'applications',
      label: 'Applications:',
      value: '',
      suffix: '',
      color: undefined
    },
    {
      id: 'completed',
      label: 'Completed (%):',
      value: '',
      suffix: '%',
      color: undefined
    },
    {
      id: 'missing',
      label: 'Missing Values:',
      value: '',
      suffix: '',
      color: undefined
    }
  ],
  calculateStats: (applications) => {
    let totalMissingFields = 0
    let totCompleted = 0
    let countApps = 0

    applications.forEach((app) => {
      if (app.relevantForCalculations) {
        totalMissingFields += app.missingFields
        totCompleted += app.completed
        countApps++
      }
      if (app.children && app.children.length > 0) {
        app.children.map((child) => {
          if (child.relevantForCalculations) {
            totalMissingFields += child.missingFields
            totCompleted += child.completed
            countApps++
          }
        })
      }
    })

    const totalCompleted = totCompleted / (100 * countApps) * 100

    return dataQualityDashboard.stats.map(stat => ({
      ...stat,
      value: stat.id === 'completed' ? `${totalCompleted.toFixed(1)}` : stat.id === 'missing' ? `${totalMissingFields}` : `${countApps}`,
      color: stat.id === 'applications' ? undefined : stat.id === 'completed' ? statColor(totalCompleted) : stat.id === 'missing' ? statColor(totalCompleted) : undefined
    }))
  },

  parse: (appsRaw: any[]) => {
    const applications: Application[] = []

    appsRaw.filter(app => !app.name.toLowerCase().includes('(poc)')).map((app) => {
      const roles = getApplicationRoles(app.subscriptions)

      const application: Application = {
        id: app.id,
        name: app.name,
        level: app.level,
        corpUnit: getCorpUnit(app.name),
        sourcing: lx.translateFieldValue('Application', 'sourcing', app.sourcing),
        softwareType: lx.translateFieldValue('Application', 'softwareType', app.softwareType),
        guiType: app.guiType ? [...app.guiType?.map((t: any) => (lx.translateFieldValue('Application', 'guiType', t)))] : undefined,
        lifecycle: app.lifecycle ? app.lifecycle as Lifecycle : undefined,
        domain: parseDomain(app.relApplicationToDomain),
        businessCapabilities: parseBusinessCapability(app.relApplicationToBusinessCapability),
        appOwnerTechnical: roles.applicationOwnerTechnical,
        appOwnerFunctional: roles.applicationResponsible,
        businessAreaOwner: getUserGroupOwner(app.relApplicationToUserGroup),
        allBusinessAreaOwners: getUserGroupAllOwner(app.relApplicationToUserGroup),
        validBusinessAreaOwner: true,
        description: app.description,
        missingFields: 0,
        completed: 100,
        baddestScore: 100,
        children: undefined,
        relevantForCalculations: true,
        hasL3: false,
        l3Apps: undefined

      }

      if (app.relToChild.edges.length > 0) {
        application.children = app.relToChild.edges.filter((child: any) => child.node.factSheet.lifecycle?.asString !== 'endOfLife' && !child.node.factSheet.name.toLowerCase().includes('(poc)')).map((child: any) => {
          const childApp: Application = {
            id: child.node.factSheet.id,
            name: child.node.factSheet.name,
            level: child.node.factSheet.level,
            corpUnit: getCorpUnit(child.node.factSheet.name),
            sourcing: lx.translateFieldValue('Application', 'sourcing', child.node.factSheet.sourcing),
            softwareType: lx.translateFieldValue('Application', 'softwareType', child.node.factSheet.softwareType),
            guiType: child.node.factSheet.guiType ? [...child.node.factSheet.guiType?.map((t: any) => (lx.translateFieldValue('Application', 'guiType', t)))] : undefined,
            lifecycle: child.node.factSheet.lifecycle ? child.node.factSheet.lifecycle as Lifecycle : undefined,
            domain: parseDomain(child.node.factSheet.relApplicationToDomain),
            businessCapabilities: parseBusinessCapability(child.node.factSheet.relApplicationToBusinessCapability),
            appOwnerTechnical: getApplicationRoles(child.node.factSheet.subscriptions).applicationOwnerTechnical,
            appOwnerFunctional: getApplicationRoles(child.node.factSheet.subscriptions).applicationResponsible,
            businessAreaOwner: getUserGroupOwner(child.node.factSheet.relApplicationToUserGroup),
            allBusinessAreaOwners: getUserGroupAllOwner(child.node.factSheet.relApplicationToUserGroup),
            validBusinessAreaOwner: true,
            description: child.node.factSheet.description,
            missingFields: 0,
            completed: 100,
            baddestScore: 100,
            children: undefined,
            relevantForCalculations: true,
            hasL3: child.node.factSheet.relToChild?.edges?.length > 0,
            l3Apps: child.node.factSheet.relToChild?.edges?.length > 0
              ? [...child.node.factSheet.relToChild?.edges.map((c: any) => ({
                  id: c.node.factSheet.id,
                  name: c.node.factSheet.name
                }))]
              : undefined
          }

          validateDataQuality(childApp)
          return childApp
        })
      }

      validateDataQuality(application)

      applications.push(application)
    })

    return applications.sort((a, b) => a.name.localeCompare(b.name))
  },

  getColumns: (apps, sortedInfo, filteredInfo, searchText, highlightText) => {
    const columns: ColumnsType<BaseApplication> = [
      {
        title: 'C.',
        width: 90,
        dataIndex: 'completed',
        key: 'completed',
        render: (text: string, record) => <CompleteScoreView app={record} />,
        filters: createFilters('completed', apps),
        filteredValue: filteredInfo.completed || null,
        onFilter: (value: boolean | Key, record): boolean => {
          const isMainMatch = value === 'no_value' ? !record.completed : record.completed === value
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.completed : child.completed === value) ?? false
          return isMainMatch || !!isChildMatch
        },
        sorter: (a, b) => a.baddestScore - b.baddestScore,
        sortOrder: sortedInfo.columnKey === 'completed' ? sortedInfo.order : null
      },

      {
        title: 'Wizard',
        width: 62,
        render: (value: any, record) => <WizardLink appId={record.id} score={record.completed} />
      },
      {
        title: 'Name',
        width: 300,
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record) => (
          <a
            dangerouslySetInnerHTML={{ __html: highlightText(text, searchText) }}
            onClick={() => lx.openLink(`/factsheet/Application/${record.id}`, '_blank')}
          >
          </a>
        ),
        sorter: (a, b) => {
          const aName = a.name.toLowerCase()
          const bName = b.name.toLowerCase()
          if (aName !== bName) { return aName.localeCompare(bName) }
          if (a.children && b.children) {
            const aChildName = a.children.map(child => child.name.toLowerCase()).join(',')
            const bChildName = b.children.map(child => child.name.toLowerCase()).join(',')
            return aChildName.localeCompare(bChildName)
          }
          return 0
        },
        sortOrder: sortedInfo.columnKey === 'name' ? sortedInfo.order : null
      },
      {
        title: (
          <Space>
            <Text>L3</Text>
            <ValidationInfo text="Level 3 Applications are deprecated." />
          </Space>
        ),
        width: 60,
        dataIndex: 'hasL3',
        key: 'hasL3',
        render: (text: string, record) => <L3Field app={record as DataQualityApplication} />
      },
      {
        title: (
          <Space>
            <Text>Descr.</Text>
            <ValidationInfo
              text="Description is required and should be at least 50 characters long."
            />
          </Space>
        ),
        dataIndex: 'description',
        key: 'description',
        ellipsis: { showTitle: false },
        render: (value: any) => (<DescriptionValue value={value} />),
        sorter: (a, b) => (a.description || '').toLowerCase().localeCompare((b.description || '').toLowerCase()),
        sortOrder: sortedInfo.columnKey === 'description' ? sortedInfo.order : null
      },
      {
        title: 'Sourcing',
        dataIndex: 'sourcing',
        key: 'sourcing',
        ellipsis: { showTitle: false },
        render: (value: any) => (<FieldValue value={value} />),
        sorter: (a, b) => (a.sourcing || '').toLowerCase().localeCompare((b.sourcing || '').toLowerCase()),
        filters: createFilters('sourcing', apps),
        filteredValue: filteredInfo.sourcing || null,
        onFilter: (value: boolean | Key, record): boolean => {
          const isMainMatch = value === 'no_value' ? !record.sourcing : record.sourcing === value
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.sourcing : child.sourcing === value) ?? false
          return isMainMatch || !!isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'sourcing' ? sortedInfo.order : null
      },
      {
        title: 'Software Type',
        dataIndex: 'softwareType',
        key: 'softwareType',
        ellipsis: { showTitle: false },
        render: (value: any) => (<FieldValue value={value} />),
        sorter: (a, b) => (a.softwareType || '').toLowerCase().localeCompare((b.softwareType || '').toLowerCase()),
        filters: createFilters('softwareType', apps),
        filteredValue: filteredInfo.softwareType || null,
        onFilter: (value: boolean | Key, record): boolean => {
          const isMainMatch = value === 'no_value' ? !record.softwareType : record.softwareType === value
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.softwareType : child.softwareType === value) ?? false
          return isMainMatch || !!isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'softwareType' ? sortedInfo.order : null
      },
      {
        title: 'GUI Type',
        dataIndex: 'guiType',
        key: 'guiType',
        ellipsis: { showTitle: false },
        render: (value: any) => (<FieldValue value={value} />),
        sorter: (a, b) => (a.guiType?.length || 0) - (b.guiType?.length || 0),
        filters: createFilters('guiType', apps),
        filteredValue: filteredInfo.guiType || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.guiType : record.guiType?.includes(value) ?? false
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.guiType : child.guiType?.includes(value) ?? false) ?? false
          return isMainMatch || isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'guiType' ? sortedInfo.order : null
      },
      {
        title: 'Lifecycle',
        key: 'lifecycle',
        width: 85,
        ellipsis: { showTitle: false },
        render: (value: any, record) => <LifeCycleView application={record} />,
        sorter: (a, b) => (a.lifecycle?.phases.length || 0) - (b.lifecycle?.phases.length || 0),
        sortOrder: sortedInfo.columnKey === 'lifecycle' ? sortedInfo.order : null
      },
      {
        title: 'Application Domain',
        dataIndex: 'domain',
        key: 'domain',
        ellipsis: { showTitle: false },
        render: (value: string[] | undefined) => (<FieldValue value={value} />),
        sorter: (a, b) => (a.domain?.length || 0) - (b.domain?.length || 0),
        filters: createFilters('domain', apps),
        filteredValue: filteredInfo.domain || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? !record.guiType : record.guiType?.includes(value) ?? false
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.guiType : child.guiType?.includes(value) ?? false) ?? false
          return isMainMatch || isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'domain' ? sortedInfo.order : null
      },
      {
        title: 'Responsible Application Owner (technical)',
        dataIndex: 'appOwnerTechnical',
        key: 'appOwnerTechnical',
        ellipsis: { showTitle: false },
        render: (value: any, record) => (<FieldValueRole value={record.appOwnerTechnical} />),
        sorter: (a, b) => (a.appOwnerTechnical?.length || 0) - (b.appOwnerTechnical?.length || 0),
        filters: createFilters('appOwnerTechnical', apps),
        filteredValue: filteredInfo.appOwnerTechnical || null,
        onFilter: (value: any, record) => {
          const isMainMatch = value === 'no_value' ? (record.appOwnerTechnical?.length ?? 0) === 0 : record.appOwnerTechnical?.includes(value) ?? false
          const isChildMatch = record.children?.some(child => value === 'no_value' ? (child.appOwnerTechnical?.length ?? 0) === 0 : child.appOwnerTechnical?.includes(value) ?? false) ?? false
          return isMainMatch || isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'appOwnerTechnical' ? sortedInfo.order : null
      },
      {
        title: 'Responsible Application Owner (functional)',
        dataIndex: 'appOwnerFunctional',
        key: 'appOwnerFunctional',
        ellipsis: { showTitle: false },
        render: (value: any, record) => (<FieldValueRole value={record.appOwnerFunctional} />),
        sorter: (a, b) => (a.appOwnerFunctional?.length || 0) - (b.appOwnerFunctional?.length || 0),
        filters: createFilters('appOwnerFunctional', apps),
        filteredValue: filteredInfo.appOwnerFunctional || null,
        onFilter: (value: any, record): boolean => {
          const isMainMatch = value === 'no_value' ? (record.appOwnerFunctional?.length ?? 0) === 0 : record.appOwnerFunctional?.includes(value) ?? false
          const isChildMatch = record.children?.some(child => value === 'no_value' ? (child.appOwnerFunctional?.length ?? 0) === 0 : child.appOwnerFunctional?.includes(value) ?? false) ?? false
          return isMainMatch || isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'appOwnerFunctional' ? sortedInfo.order : null
      },
      {
        title: 'Business Area Owner',
        dataIndex: 'businessAreaOwner',
        key: 'businessAreaOwner',
        width: 150,
        ellipsis: { showTitle: false },
        render: (value: any, record) => (<BusinessAreaOwnerField app={record} value={value} />),
        sorter: (a, b) => (a.businessAreaOwner || '').toLowerCase().localeCompare((b.businessAreaOwner || '').toLowerCase()),
        filters: createFilters('businessAreaOwner', apps),
        filteredValue: filteredInfo.businessAreaOwner || null,
        onFilter: (value: any, record): boolean => {
          const isMainMatch = value === 'no_value' ? !record.businessAreaOwner : record.businessAreaOwner === value
          const isChildMatch = record.children?.some(child => value === 'no_value' ? !child.businessAreaOwner : child.businessAreaOwner === value) ?? false
          return isMainMatch || isChildMatch
        },
        sortOrder: sortedInfo.columnKey === 'businessAreaOwner' ? sortedInfo.order : null
      }
    ]

    return columns
  },

  export: (xlsx, applications) => {
    const prepareData = (apps: DataQualityApplication[]) => {
      const createRow = (app: DataQualityApplication) => ({
        'Completed': app.completed,
        'ID': app.id,
        'Name': app.name,
        'Level': app.level,
        'L3': app.hasL3 ? 'YES' : 'NO',
        'Description': app.description ? app.description : '',
        'Sourcing': app.sourcing ? app.sourcing : '',
        'Software Type': app.softwareType ? app.softwareType : '',
        'GUI Type': app.guiType?.join('; '),
        'Lifecycle: Phase In': getLifecyclePhase('phaseIn', app.lifecycle) || '',
        'Lifecycle: Go-Live': getLifecyclePhase('active', app.lifecycle) || '',
        'Lifecycle: Phase Out': getLifecyclePhase('phaseOut', app.lifecycle) || '',
        'Application Domain': app.domain ? app.domain?.join('; ') : '',
        'Application Owner (technical)': app.appOwnerTechnical.join('; '),
        'Application Owner (functional)': app.appOwnerFunctional.join('; '),
        'Business Area Owner': app.businessAreaOwner ? app.businessAreaOwner : ''
      })

      const parsed: any[] = []

      apps.map((app) => {
        if (app.relevantForCalculations) { parsed.push(createRow(app)) }

        if (app.children && app.children.length > 0) {
          app.children.map((child) => {
            if (child.relevantForCalculations) { parsed.push(createRow(child as DataQualityApplication)) }
          })
        }
      })
      return parsed
    }

    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(
      prepareData(applications as DataQualityApplication[])
    )

    XLSX.utils.book_append_sheet(wb, ws_application, 'Data Quality')
    XLSX.writeFile(wb, xlsx ? 'export.xlsx' : 'export.csv')
  }
}
