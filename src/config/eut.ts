import {WizardFormModel} from "../model";
import {useLeaniX} from "../hooks/leanix";


const { removeFactSheet } = useLeaniX();

export const eutForm:WizardFormModel = {

    id: "addEUT",
    name: "Add EUT",
    originFactSheet: "ITComponent",
    addWithTemporaryMode: false,
    onlyAdmin: false,
    saveErrorMessage: "Error while creating. Please try it again!",
    saveSuccessMessage: "EUT successfully created",
    steps: [
        {
            name: "Information",
            rules: [],
            fields: [
                {
                    title: 'Prefix',
                    name: 'prefix',
                    fieldType: 'Hidden',
                    description: 'just the prefix',
                    required: true,
                    visible: false
                },
                {
                    title: "Name",
                    name: "name",
                    fieldType: "NameField",
                    description: "The name is used to identify this Fact Sheet in the Inventory, Reporting and Search.",
                    required: true,
                    visible: true
                },
                {
                    title: "Description",
                    name: "description",
                    fieldType: "TextArea",
                    description: "Please provide a meaningful description to enable other users to understand the main purpose.",
                    required: true,
                    visible: true
                },
                {
                    title: "Tool and/or programming language",
                    name: "toolProgrammingLanguage",
                    fieldType: "TextArea",
                    description: "Specifies the primary tools and programming languages used in the development and maintenance of the EUT.",
                    required: true,
                    visible: true
                },
                {
                    title: "Release",
                    name: "release",
                    fieldType: "Text",
                    description: "\"MAJOR\" or \"MAJOR.MINOR\" version sematic. Please create/find a new component if you want to update the version!",
                    required: true,
                    visible: true
                },
                {
                    title: "Place of Storage",
                    name: "placeOfStorage",
                    fieldType: "Text",
                    description: "",
                    required: true,
                    visible: true
                },

                ],
            customChecks: async () => {
                return undefined
            }
        },
        {
            name: 'Subscriptions',
            rules: [],
            fields: [
                {
                    name: 'eutResponsible',
                    title: 'EUT Responsible',
                    description: 'The EUT Responsible (functional) is typically the Process Owner when the EUT is part ' +
                        'of a Process and Key Control. This role involves overall functional responsibility for the EUT, ' +
                        'including identifying it, appointing an EUT Owner (technical), and determining its criticality' +
                        ' (e.g. ICOR Relevancy) in collaboration with the EUT Owner (technical).',
                    fieldType: 'UserSelectMultiple',
                    required: true,
                    visible: true
                },
                {
                    name: 'eutOwner',
                    title: 'EUT Owner',
                    description: 'The EUT Owner (technical) is responsible for developing and maintaining the EUT. ' +
                        'As a technical expert, they ensure proper documentation and upkeep of the EUT, assess control ' +
                        'requirements, and provide justification for any non-applicable control requirements. They also ' +
                        'perform and document EUT controls as well as conduct both control and risk assessments.',
                    fieldType: 'UserSelectMultiple',
                    required: true,
                    visible: true
                }
            ],
            customChecks: async () => {
                return undefined
            }
        }

    ],
    init: (form) => {
        form.setFieldValue('prefix', 'CF-')
    },
    save: async (fsData) => {

        console.log("SAVE EUT", fsData);

        let compId = '';

        try{
            const name = `${fsData.prefix}${fsData.name}`;

        }catch (err) {
            console.error('ERROR', err)
            console.log(compId)
            if (compId) {
                await removeFactSheet(compId)
            }
            return undefined
        }




        return compId;


    }
}