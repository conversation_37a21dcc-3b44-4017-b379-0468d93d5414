import type { FactSheetData, WizardFormModel } from '../../model'
import dayjs from 'dayjs'
import { ADD_CONSUMER_MUTATION } from '../../graphql'

export const addKafkaConsumer: WizardFormModel = {

  id: 'information-flow/kafka-consumer',
  name: 'Information Flow - Add Kafka Consumer',
  originFactSheet: 'Interface',
  addWithTemporaryMode: false,
  onlyAdmin: false,
  saveErrorMessage: 'Error while creating. Please try it again!',
  saveSuccessMessage: 'Kafka Consumer successfully created',
  steps: [
    {
      name: 'Select Interface',
      fields: [
        {
          title: 'Select Interface',
          name: 'selectedInterface',
          description: '',
          fieldType: 'SelectFactsheetTable',
          loadFactSheet: 'Interface',
          required: true,
          visible: true
        }
      ],
      rules: [],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Consumer Details',
      fields: [
        {
          title: 'Application',
          name: 'application',
          description: '',
          fieldType: 'SingleSelect',
          loadFactSheet: 'Application',
          required: true,
          visible: true
        },
        {
          title: 'Lifecycle Active',
          description: '',
          name: 'active',
          required: true,
          fieldType: 'DatePicker',
          visible: true
        },
        {
          title: 'Lifecycle To',
          description: '',
          name: 'endOfLife',
          required: false,
          fieldType: 'DatePicker',
          visible: true
        },
        {
          title: 'Description',
          description: '',
          name: 'description',
          required: true,
          fieldType: 'TextArea',
          visible: true
        }
      ],
      rules: [],
      customChecks: async () => {
        return undefined
      }
    }
  ],

  init(): void {

  },

  async save(data: FactSheetData): Promise<string | undefined> {
    console.log('KAFKA Consumer', data)

    const interfaceId = data.selectedInterface.id

    const mutation = ADD_CONSUMER_MUTATION.replace('$interfaceId', interfaceId)

    const variables = {
      patches: [
        {
          op: 'add',
          path: '/relInterfaceToConsumerApplication/new',
          value: JSON.stringify({
            description: data.description,
            factSheetId: data.application,
            activeFrom: dayjs(data.active).format('YYYY-MM-DD'),
            ...(data.endOfLife && {
              activeUntil: dayjs(data.endOfLife).format('YYYY-MM-DD')
            })
          })
        }
      ]
    }

    try {
      const result = await lx.executeGraphQL(mutation, JSON.stringify(variables))
      console.log(result)
      return result.result.factSheet.id
    }
    catch (error: any) {
      console.log(error)
      return undefined
    }
  }
}
