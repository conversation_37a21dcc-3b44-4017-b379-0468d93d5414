


export type DashboardType = 'Cloud Journey' | 'Architecture Domain';

export interface ApplicationModel {
    id: string,
    rowid: string,
    name: string,
    corpUnit: string,
    applicationType: string,
    sourcing: string,
    domain: string,
    lifeCycle: string,
    cloudTargetSourcing: string,
    cloudTargetDate: Date,
    domainTag: string,
    valueToTake: string;
}

export interface PieModel {
    type: string,
    value: number,
    color: string
}


export interface PreviewWrapper {
    date: Date
    data: ApplicationModel[]
}

export type LoadDataResult = {
    liveData: PreviewWrapper;
    nextYearData: PreviewWrapper;
    secondYearData: PreviewWrapper;
    thirdYearData: PreviewWrapper;
};

export interface DomainModel {
    domainName: string,
    apps: ApplicationModel[]
}

export interface DomainCategoryModel {
    categoryName: string,
    domains: DomainModel[]
}

export interface DomainWrapper {
    date: Date
    categories: DomainCategoryModel[]
}

export interface BarChartModel {
    name: string,
    data: number[]
}