import type { ColumnsType } from 'antd/es/table'
import type { TableProps } from 'antd/lib'

export interface Lifecycle {
  asString: string
  phases: [{ phase: string, startDate: string }]
}

export interface Value {
  id: string
  name: string
}

export interface BaseApplication {
  id: string
  name: string
  level: number
  corpUnit: string
  description: string | undefined
  sourcing: string | undefined
  softwareType: string | undefined
  guiType: string[] | undefined
  lifecycle: Lifecycle | undefined
  domain: string[] | undefined
  businessCapabilities: string[] | undefined
  appOwnerTechnical: string[]
  appOwnerFunctional: string[]
  businessAreaOwner: string | undefined
  allBusinessAreaOwners: string[]
  validBusinessAreaOwner: boolean
  completed: number
  missingFields: number
  children: Application[] | undefined
  relevantForCalculations: boolean
  baddestScore: number

}

export interface DataQualityApplication extends BaseApplication {
  hasL3: boolean
  l3Apps: Value[] | undefined
}

export interface RessortApplication extends BaseApplication {
  userAccessDataCategory: string[]
  hostingProviderCountry: string[]
  relApplicationToHostingProvider: Value[]
  relApplicationToOperationsProvider: Value[]
  relApplicationToSoftwareServiceProvider: Value[]
  itRessort: ITRessort | undefined
};

export interface Statistic {
  id: string
  label: string
  color: string | undefined
  value: string
  suffix: string
}

export type FILTERS = 'textSearch' | 'itRessorts' | 'corpUnits' | 'corpUnitsSmall' | 'unexpandAll'

export interface Dashboard {
  id: string
  name: string
  stats: Statistic[]
  filters: FILTERS[]
  calculateStats: (applications: Application[]) => Statistic[]
  export: (xlsx: boolean, applications: Application[]) => void
  parse: (data: any[]) => (Application[])
  getColumns: (apps: Application[], sortedInfo: Sorts, filteredInfo: FType, searchText: string, highlightText: (
    text: string,
    search: string,
  ) => string) => ColumnsType<(Application)>
}

export interface ITRessort {
  id: string
  name: string
  description: string
  color: string
  appCount: number
  completed: number
  missing: number
}

export type OnChange = NonNullable<TableProps<Application>['onChange']>
export type FType = Parameters<OnChange>[1]

export type GetSingle<T> = T extends (infer U)[] ? U : never
export type Sorts = GetSingle<Parameters<OnChange>[2]>

export interface FilterOption {
  text: React.ReactNode
  value: string | number | boolean
}
export type Filters = Parameters<OnChange>[1]
export type Application = DataQualityApplication | RessortApplication | BaseApplication
