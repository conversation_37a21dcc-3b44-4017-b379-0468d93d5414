import type { AppFactSheet, LifecyclePhase } from '@app/models/data'

const isPhaseOrderCorrect = (phases: LifecyclePhase[]): boolean => {
  const phaseOrder: string[] = ['plan', 'phaseIn', 'active', 'phaseOut', 'endOfLife']
  const sortedPhases = phases.slice().sort((a, b) => new Date(a.startDate as string).getTime() - new Date(b.startDate as string).getTime())

  let lastPhaseIndex = -1
  let lastDate: number | null = null

  for (const phaseData of sortedPhases) {
    const currentPhaseIndex = phaseOrder.indexOf(phaseData.phase)
    const currentPhaseDate = new Date(phaseData.startDate as string).getTime()

    // Wenn die aktuelle Phase in der Reihenfolge nicht gefunden wird
    if (currentPhaseIndex === -1) {
      return false
    }

    // Wenn die aktuelle Phase ein früheres Datum hat oder die Phase-Reihenfolge verletzt wird
    if (currentPhaseDate < (lastDate ?? currentPhaseDate) || (currentPhaseDate === lastDate && currentPhaseIndex < lastPhaseIndex)) {
      return false
    }

    lastPhaseIndex = currentPhaseIndex
    lastDate = currentPhaseDate
  }

  return true
}

export const parseSingleAppLifeCycle = (app: AppFactSheet) => {
  app.updatedLifecycle = false

  const active = app.planningGoLiveMilestone1
  const phaseOut = app.planningSwitchOffMilestone4

  const originalLifecycle = JSON.parse(JSON.stringify(app.lifecycle))
  app.originalLifecycle = originalLifecycle

  if (active.length !== 0 || phaseOut.length !== 0) {
    // RAND CASE WENN SYNC NICHT GEHT
    if (!app.lifecycle) {
      app.lifecycle = {
        phases: []
      }
    }

    app.lifecycle.phases.map((phase) => {
      if (phase.phase === 'active' && phase.startDate != active && active.length > 0) {
        // DATUM überschreiben
        phase.startDate = active
        app.updatedLifecycle = true
      }

      if (phase.phase === 'phaseOut' && phase.startDate != phaseOut && phaseOut.length > 0) {
        // DATUM überschreiben
        phase.startDate = phaseOut
        app.updatedLifecycle = true
      }
    })

    // add active phase
    if (active.length > 0 && app.lifecycle.phases.filter(p => p.phase === 'active').length === 0) {
      app.lifecycle.phases.push({
        startDate: active,
        phase: 'active'
      } as LifecyclePhase)
      app.updatedLifecycle = true
    }

    // add end of life phase
    if (phaseOut.length > 0 && app.lifecycle.phases.filter(p => p.phase === 'phaseOut').length === 0) {
      app.lifecycle.phases.push({
        startDate: phaseOut,
        phase: 'phaseOut'
      } as LifecyclePhase)
      app.updatedLifecycle = true
    }

    // if (app.updatedLifecycle) {
    // app.originalLifecycle = originalLifecycle;
    // }

    app.validLifecycle = isPhaseOrderCorrect(app.lifecycle.phases)
  }
  else {
    app.updatedLifecycle = false
    app.validLifecycle = app.lifecycle ? isPhaseOrderCorrect(app.lifecycle.phases) : true
  }
  return app
}

export const parseLifecycle = (apps: AppFactSheet[]) => {
  return apps.map((app) => {
    return parseSingleAppLifeCycle(app)
  })
}
