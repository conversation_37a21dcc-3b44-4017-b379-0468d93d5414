const parseString = (str: string, start: number, end: number): number => {
  let res: string | number = ''
  for (let i = 0; i < end - start + 1; i++) {
    res = res + str[start + i]
  }
  return Number(res)
}

/* FALSCH BEI SCHALTJAHREN
const dateToInt = (date: string): number => {

  let days: number = parseString(date, 0, 3) * 365
  days = days + Math.floor(parseString(date, 0, 3) / 4)
  const month = (parseString(date, 5, 6) > 10 ? String(parseString(date, 5, 6)) : '0' + String(parseString(date, 5, 6)))
  days = (days + monthsDays[month]) + Math.floor(parseString(date, 8, 9))

  return days
}
 */

const dateToInt = (date: string): number => {
  // Convert dates to integer representing the number of days from year 0
  let year: number = parseString(date, 0, 3);
  let month: number = parseString(date, 5, 6);
  let day: number = parseString(date, 8, 9);

  let days: number = year * 365 + Math.floor(year / 4);

  // Format month for key
  let monthKey: string = month < 10 ? '0' + month.toString() : month.toString();

  days += monthsDays[monthKey];

  // Adjust for leap year if date is after February in a leap year
  if (year % 4 === 0 && month > 2) {
    days += 1;
  }

  days += day;

  return days;
}

const mDays: MonthDays = {
  '01': 31,
  '02': 28,
  '03': 31,
  '04': 30,
  '05': 31,
  '06': 30,
  '07': 31,
  '08': 31,
  '09': 30,
  '10': 31,
  '11': 30,
  '12': 31
}

interface MonthDays {
  [key: string]: number
}

const monthsDays: MonthDays = {
  '01': 31,
  '02': 59,
  '03': 90,
  '04': 120,
  '05': 151,
  '06': 181,
  '07': 212,
  '08': 243,
  '09': 273,
  '10': 304,
  '11': 334,
  '12': 365
}


export { parseString, dateToInt, monthsDays, mDays };
