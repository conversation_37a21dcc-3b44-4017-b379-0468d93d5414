// Import LeanIX reporting library
import '@leanix/reporting'

// Import helper functions
import { sortFactsheets } from './Sorting'

// Import types 
import { AppClusters, AppFactSheet, TagColors } from '../models/data'
import { Tag, AllTags } from '../models/clustering.models'


export var initialReportSettingsValues: lxr.FormModalValues = {
  startDate: (new Date().getFullYear() - 1) + '-01-01',
  endDate: (new Date().getFullYear() + 6) + '-01-01',
  clusterBy: '',
  drillDown: ''
}

export function clusterApplications(apps: any, by: string) {

  const sortedApps = sortFactsheets(apps, 'cluster', by)
  apps = [...sortedApps]

  return apps
}
export var allTags: AllTags = {}

export async function loadTags() {
  return fetchTagValues().then((tags: Tag[]) => {

    const tagsDict: any = {}
    const tagsColors: TagColors = {}
    tags.forEach((tag: any) => {

      if (tag.tagGroup === null || tag.tagGroup.restrictToFactSheetTypes.includes('Application')) {

        if (tag.tagGroup === null) {
          tag.tagGroup = { id: 'otherTags' }
        }

        if (tag.tagGroup.id in tagsDict) {
          if (Array.isArray(tagsDict[tag.tagGroup.id].values)) {
            tagsDict[tag.tagGroup.id].values.push(tag.name)
            tagsColors[tag.tagGroup.id + tag.name] = tag.color
          }
        } else {
          tagsDict[tag.tagGroup.id] = {
            name: tag.tagGroup.name,
            values: [tag.name]
          }
          tagsColors[tag.tagGroup.id + tag.name] = tag.color
        }
      }
    })
    return [tagsDict, tagsColors]
  })

}

function fetchTagValues(): any {
  return lx.executeGraphQL(`{
    allTags {
     edges
      {
        node {
          id
          name
          color
          tagGroup {
            id name restrictToFactSheetTypes
          }
        }
      }
    }
  }`).then(({ allTags: { edges } }) => edges.map(({ node }: any) => node))
}

export function groupAppsByClusters(apps: AppFactSheet[], appRelations: any, allTags: any, tagGroupId: string): AppClusters {
  /**
   * Cluster applications into a dictionary with keys as cluster names and array of apps as values
  */

  const formatName = (name: string) => {

    const isLevel2 = name.includes("/")


    return isLevel2 ? `${name.split('/')[1].trim()} (${name.split('/')[0]})` : name;

  }


  const appClusters: any = {}

  // Check if the chosen item to cluster by is a tag or a relation 

  // ***********************************
  // If User is clustering by a relation
  // ***********************************
  if (tagGroupId.slice(0, 4) == 'rel_') {
    const relationId = tagGroupId.slice(4)
    appClusters['No Cluster'] = { apps: [] }
    apps.sort((a,b) => formatName(a.displayName.toLowerCase()).localeCompare(formatName(b.displayName.toLowerCase()))).forEach((app: AppFactSheet) => {

      if (appRelations[app.id][relationId].length === 0) {
        appClusters['No Cluster'].apps.push(app)
        return
      }

      for (let i = 0; i < appRelations[app.id][relationId].length; i++) {
        const relationName = appRelations[app.id][relationId][i].displayName
        if (Object.keys(appClusters).includes(relationName)) {
          appClusters[relationName].apps.push(app)
        }
        else {
          appClusters[relationName] = { apps: [app] }
        }
        //appClusters[relationId].apps.push(app)
      }
    })
  } else {

    // ******************************
    // If User is clustering by a tag
    // ******************************
    for (let i = 0; i < allTags[tagGroupId].values.length; i++) {
      appClusters[allTags[tagGroupId].values[i]] = { apps: [] }
    }
    appClusters['No Cluster'] = { visible: true, apps: [] }

    apps.forEach((app: AppFactSheet) => {


      if (Object.keys(app.tags).includes(tagGroupId)) {
        for (let i = 0; i < app.tags[tagGroupId].length; i++) {
          appClusters[app.tags[tagGroupId][i]].apps.push(app)
        }
      } else {
        appClusters['No Cluster'].apps.push(app)
      }
    })
  }


  const customSort = (object:any) => {
    const keys = Object.keys(object);

    keys.sort((a, b) => {
      if (a === "No Cluster" && b !== "No Cluster") {
        return 1; // "No Cluster" should come after other keys
      } else if (a !== "No Cluster" && b === "No Cluster") {
        return -1; // "No Cluster" should come before other keys
      } else {
        return a.localeCompare(b); // Alphabetical sorting for other keys
      }
    });

    const sortedObject:any = {};
    keys.forEach(key => {
      sortedObject[key] = object[key];
    });

    return sortedObject;
  }



  return customSort(appClusters);
}
