export const customMilestonesAttributes = [
  'planningGoLiveMilestone1',
  'planningGoLiveMilestone2',
  'planningGoLiveMilestone3',
  'planningGoLiveMilestone4',
  'planningGoLiveCommentM1',
  'planningGoLiveCommentM2',
  'planningGoLiveCommentM3',
  'planningGoLiveCommentM4',
  'planningSwitchOffMilestone1',
  'planningSwitchOffMilestone2',
  'planningSwitchOffMilestone3',
  'planningSwitchOffMilestone4',
  'planningSwitchOffCommentM1',
  'planningSwitchOffCommentM2',
  'planningSwitchOffCommentM3',
  'planningSwitchOffCommentM4'
]

export function fetchTagAttribute(tagGroup: string | lxr.FormModalValue | null, retrieveNames: boolean = false): string {
  if (tagGroup == '') { return '' }
  if (retrieveNames) {
    return 'tags { id name tagGroup { id name} }'
  }
  else {
    return 'tags { id tagGroup { id } }'
  }
}
