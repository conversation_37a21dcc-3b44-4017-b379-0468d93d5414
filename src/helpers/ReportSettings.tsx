// Import leanIX reporting library
import '@leanix/reporting'

// Import types
import { TagGroupNode, reportSettings } from '@app/models/clustering.models'
import { AppRelations } from '@app/models/data'

export const initialReportSettingsValues = {
  startDate: (new Date().getFullYear() - 1) + '-01-01',
  endDate: (new Date().getFullYear() + 6) + '-01-01',
  clusterBy: '',
  drillDown: ''
}

export async function configureSettingsFields(appRelations: AppRelations) {
  // Set fields

  async function getSettingFields() {

    // Relations to use in Cluster By - plus adding 'rel_' prefix
    const clusterGroupRelations = [{ value: 'rel_relApplicationToDomain', label: 'Domain' }, { value: 'rel_relApplicationToBusinessCapability', label: 'Business Capability' }]
    const clusterOptions = [...clusterGroupRelations, ...await getTagsClusterOptions()]

    const drillDownOptions = appRelations.map((relation: string) => {
      return {
        value: relation,
        label: lx.translateRelation(relation)
      }

    })
    //const clusterOptions = ['domain', 'BusinessCapability', 'UserGroup']

    const settingsFields: reportSettings = {

      clusterBy: {
        label: 'Cluster By',
        type: 'SingleSelect',
        options: clusterOptions,
        allowClear: true
      },
      drillDown: {
        label: 'Drill Down',
        type: 'SingleSelect',
        options: drillDownOptions,
        allowClear: true
      },
      startDate: {
        label: 'Start Date',
        type: 'Date',
      },

      endDate: {
        label: 'End Date',
        type: 'Date',
      }
    }

    return settingsFields
  }
  const settingsFields = await getSettingFields()

  return settingsFields
}

async function getTagsClusterOptions() {
  const getTagGroups = lx.executeGraphQL(`{
        allTagGroups {edges {
          node {
            name
            id
            restrictToFactSheetTypes
          }
        }}
      }`)

  const tagGroups = await getTagGroups
  const clusterOptions = tagGroups.allTagGroups.edges.reduce((tags: any, tagGroup: any) => {

    if (tagGroup.node.restrictToFactSheetTypes.includes('Application')) {
      tags.push(tagGroup)

    }
    return tags

  }, [])

  return getOptions(clusterOptions)
}



//    return options.map((option) => {
//    })
//
//    return options.map((option): lxr.FormModalSingleSelectFieldOption => {
//    })
//  }

function getOptions(options: TagGroupNode[]) {
  return options.map(tag => getOptionObject(tag))
}

function getOptionObject(option: TagGroupNode | string): lxr.FormModalSingleSelectFieldOption {
  if (typeof option === 'string') {
    return { value: option, label: option }
  } else {
    return { value: option.node.id, label: option.node.name }
  }
}



