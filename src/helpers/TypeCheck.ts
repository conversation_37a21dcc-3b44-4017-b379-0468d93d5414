// Import interfaces
import { AppFactSheet, AppClusters } from '../models/data';

export function isAppClusters(apps: AppFactSheet[] | AppClusters): apps is AppClusters {
  return apps.hasOwnProperty('No Cluster')
}

export function isString(val: any): val is string {
  return typeof val == 'string'
}

export function isFormModalValues(level: string | string[] | true | lxr.FormModalValues): level is lxr.FormModalValues {
  return typeof level === 'object' && !Array.isArray(level)
}

export function startDateIsNumber(startDate: number | string | null): startDate is number {
  return (typeof startDate === 'number')
}
