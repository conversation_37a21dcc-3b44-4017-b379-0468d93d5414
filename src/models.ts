

export type CorpUnit = "AT" | "DE" | "FR" | "ITA" | "ES" | "CAS";
export type ViewMode = "groupApplications" | "marketUnitOverview"


export interface Value {
    label: string; //name
    value: string; //id
}

export interface Application {
    id:string;
    snowId:string;
    name:string;
    corpUnit:string;
    level:number;
    lifecycle:string;
    projects: Value[];
    domain: Value | undefined;
    businessAreas: Value[];
    platform:string;
    children:Application[] | undefined;
    serviceBaseLineApproach:string;
    serviceBaseLineComment:string;
    hostingCH:boolean;
}


export interface BusinessAreaFilter {
    text: string;
    value: boolean;
}

export interface ValueFilter {
    text: string;
    value: string;
}