import type { LXDocument } from '../model'
import { RESOURCE_FACT_SHEET } from '../constants'

export const useDocuments = () => {
  const parseDocuments = (data: any) => {
    return data.factSheet.documents.edges.filter((d: any) => d.node.documentType === 'ordering_form').map((d: any) => ({
      id: d.node.id,
      name: d.node.name,
      documentType: d.node.documentType,
      description: d.node.description,
      createdAt: d.node.createdAt,
      application: JSON.parse(d.node.description)
    })) as LXDocument[]
  }

  const loadDocuments = async () => {
    return lx.executeGraphQL(`{
                                              factSheet(id: "${RESOURCE_FACT_SHEET}") {
                                                id
                                                documents {
                                                  edges {
                                                    node {
                                                      id
                                                      name
                                                      description
                                                      documentType
                                                      createdAt
                                                    }
                                                  }
                                                }
                                              }
                                            }`).then(result => parseDocuments(result))
  }

  const addRessource = async (name: string, description: string) => {
    // console.log("ADD RESSOURCE")
    const response = await lx.executeGraphQL(`mutation ($id: ID!, $name: String!, $description: String!, $url: String) {
                                              createDocument(
                                                factSheetId: $id
                                                name: $name
                                                description: $description
                                                url: $url
                                                origin: "CUSTOM_LINK"
                                                documentType: "ordering_form"
                                              ) {
                                                id
                                                name
                                              }
                                                }`, JSON.stringify({
      id: RESOURCE_FACT_SHEET,
      name,
      url: 'helvetia.ch',
      description
    }))

    // console.log(response);

    return response
  }

  const updateRessource = async (documentId: string, app: { [key: string]: any }) => {
    const mutation = `mutation ($documentId: ID!, $patches: [Patch]!) {
                                                      result: updateDocument(id: $documentId, patches: $patches) {
                                                        id
                                                        name
                                                        description
                                                        url
                                                        createdAt
                                                        fileInformation {
                                                          fileName
                                                          size
                                                          mediaType
                                                          previewImage
                                                          content
                                                        }
                                                        origin
                                                        documentType
                                                        metadata
                                                        refId
                                                      }
                                                    }`

    const variables = {
      documentId: `${documentId}`,
      patches: [
        {
          op: 'replace',
          path: '/name',
          value: 'prefix' in app ? `${app.prefix}${app.name}` : app.name
        },
        {
          op: 'replace',
          path: '/description',
          value: JSON.stringify(app)
        }
      ]
    }

    return lx.executeGraphQL(mutation, JSON.stringify(variables))
  }

  const deleteRessource = async (documentId: string) => {
    return lx.executeGraphQL(`
                                                mutation {
                                          result: deleteDocument(
                                            id: "${documentId}"
       
                                          ) {
                                            id
                                            name
                                            description
                                            url
                                            createdAt
                                            fileInformation {
                                              fileName
                                              size
                                              mediaType
                                              previewImage
                                              content
                                            }
                                            origin
                                            documentType
                                            metadata
                                            refId
                                          }
                                        }
        `)
  }

  return {
    loadDocuments,
    addRessource,
    updateRessource,
    deleteRessource
  }
}
