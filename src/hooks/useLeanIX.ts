import type { Value } from '@app/models'

export const useLeanIX = () => {
  const getPlatforms = async (): Promise<Value[]> => {
    const values: string[] = (lx.currentSetup.settings.dataModel.factSheets.Application.fields.plattform as any).values

    const objects: Value[] = []

    values.map((value) => {
      objects.push({
        value,
        label: lx.translateFieldValue('Application', 'plattform', value)
      })
    })

    return objects
  }

  return {
    getPlatforms
  }
}
