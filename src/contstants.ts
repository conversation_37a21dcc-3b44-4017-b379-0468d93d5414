import type { BarChartModel, DomainCategoryModel, DomainModel, PieModel } from '@app/model'

export const PIE_COLORS = ['#8E50D0', '#92D050', '#50D06E', '#50D0CE', '#5072D0', '#8E50D0', '#D050B2', '#D05052', '#D0AE50', '#cc7178']

export const ARCHITECTURE_DOMAIN_PIE: PieModel[] = [
  {
    type: 'Cloud',
    value: 0,
    color: '#46A9B4'
  },
  {
    type: 'External Hosting',
    value: 0,
    color: '#8861A9'
  },
  {
    type: 'On-Premise',
    value: 0,
    color: '#C31622'
  }

]

const FRONT_DOMAINS: DomainModel[] = [
  { domainName: 'Client & Partner', apps: [] },
  { domainName: 'Interaction', apps: [] },
  { domainName: 'Marketing, Sales & Advisory', apps: [] },
  { domainName: 'Sales Partner Mgt', apps: [] }
]

const CORE_DOMAINS: DomainModel[] = [
  { domainName: 'ERV', apps: [] },
  { domainName: 'Life & Pensions', apps: [] },
  { domainName: 'Life & Pensions / Group Life', apps: [] },
  { domainName: 'Life & Pensions / Individual Life', apps: [] },
  { domainName: 'Non-Insurance', apps: [] },
  { domainName: 'Non-Insurance / Care', apps: [] },
  { domainName: 'Non-Insurance / Home', apps: [] },
  { domainName: 'Non-Life', apps: [] },
  { domainName: 'Partner Business', apps: [] },
  { domainName: 'Smile', apps: [] },
  { domainName: 'SpM', apps: [] },
  { domainName: 'SpM / Active Reinsurance', apps: [] },
  { domainName: 'SpM / SpL CH & Intl.', apps: [] }
]

const CROSS_DOMAINS: DomainModel[] = [
  { domainName: 'Actuarial Services', apps: [] },
  { domainName: 'Asset Management', apps: [] },
  { domainName: 'Corporate & Logistic', apps: [] },
  { domainName: 'Finance', apps: [] },
  { domainName: 'Human Resources', apps: [] },
  { domainName: 'Risk, Legal & Compliance', apps: [] }
]

const SUPPORT_DOMAINS: DomainModel[] = [
  { domainName: 'Collaboration', apps: [] },
  { domainName: 'Data & Analytics', apps: [] },
  { domainName: 'ECM', apps: [] },
  { domainName: 'Infrastructure', apps: [] },
  { domainName: 'Integration', apps: [] },
  { domainName: 'IT Management', apps: [] },
  { domainName: 'IT Security', apps: [] }
]

export const DOMAIN_CATEGORIES: DomainCategoryModel[] = [
  { categoryName: 'Front', domains: FRONT_DOMAINS },
  { categoryName: 'Core', domains: CORE_DOMAINS },
  { categoryName: 'Cross', domains: CROSS_DOMAINS },
  { categoryName: 'Support', domains: SUPPORT_DOMAINS }
]

export const CLOUD_JOURNEY_BAR: BarChartModel[] = [
  {
    name: 'Datacenter',
    data: [0, 0, 0, 0]
  },
  {
    name: 'External Hosting',
    data: [0, 0, 0, 0]
  },
  {
    name: 'Cloud',
    data: [0, 0, 0, 0]
  },
  {
    name: 'Replacement',
    data: [0, 0, 0, 0]
  }
]
