
$bg-color: yellow;
$border-color: red;

.app {
  font-family: helvetica, arial, sans-serif;
  padding: 2em;
  border: 5px solid $border-color;

  p {
    background-color: $bg-color;
  }
}

#components-layout-demo-custom-trigger .trigger {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

#components-layout-demo-custom-trigger .logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.site-layout .site-layout-background {
  background: #fff;
}

.trigger-fix {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.ant-table-cell {
  padding:0px 6px 0px 2px !important;
}

@media screen and (max-width: 768px) {
  .responsive-flex {
    flex-direction: column !important;
    align-items: flex-start !important;

    .ant-space {
      width: 100%;
    }
  }
}