import type { GlobalToken } from 'antd'
import type React from 'react'

export const getContentStyle = (token: GlobalToken): React.CSSProperties => ({
  textAlign: 'center',
  color: token.colorTextTertiary,
  backgroundColor: token.colorFillAlter,
  borderRadius: token.borderRadiusLG,
  border: `1px dashed ${token.colorBorder}`,
  marginTop: 16,
  padding: '4px'
})

// Dynamically determine if we're in production based on the hostname
export const PROD: boolean = !window.location.hostname.includes('localhost') && !window.location.hostname.includes('127.0.0.1')
// Log the environment for debugging
console.log(`Running in ${PROD ? 'production' : 'local'} environment (hostname: ${window.location.hostname})`)
export const RESOURCE_FACT_SHEET: string = '9e4efbc3-fb84-4ca1-923a-78788329087d'
export const BUSINESS_AREA_CF: string = '71d7e010-921a-41a0-aaca-01dc693316f5'
export const BUSINESS_AREA_CH: string = 'e8ec9a5c-a967-44f8-9fb8-0fd46830363c'
export const USER_ID_RAMON: string = 'c14070d9-64d6-42ee-99cc-8386a8995237'
export const USER_ID_OSCAR: string = '29bb1250-60d7-4775-8110-2ca4a46f1ff7'
export const SUB_APPLICATION_OWNER_TECHNICAL: string = '9c7a5c4c-c2a9-4b67-b43c-205b1bb715f9'
export const SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL: string = '80bcd083-f140-4a0e-b82b-b389831ddca7'

export const APACHE_KAFKA_COMPONENT_ID = '41c6dcfd-f1d1-4a62-8b4d-4fb6390ecb04'
export const KAFKA_TAG = 'f6939db3-a9be-42c1-8607-34c2817eeea1'
export const MFT_TAG = '8b550745-8045-441b-aaa2-7d06293b0103'

export const API_MIDDLEWARE = [
  '2dc29bfc-bd67-4f7a-a57f-8f9e7f3afe69',
  'dd79a10e-0082-4b7b-a958-bb57d14fdc34',
  '19565640-33c4-4fdd-9216-eff9551f4c7d',
  'e1b3f820-b9ee-44df-b700-6d8b65239bd0',
  '88f5c3a9-f49b-4fea-adc9-154fbb10bfe7'
]
