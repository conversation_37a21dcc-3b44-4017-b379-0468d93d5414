import React, {useEffect, useState} from "react";
import {Col, DatePicker, Input, Modal, Row} from "antd";
import dayjs from "dayjs";
import {AppFactSheet, MilestonesFormData, MilestonesInput} from "@app/models/data";

const fields: MilestonesInput[] = [
    {
        date: {
            label: "(First) Go-Live",
            key: "planningGoLiveMilestone1"
        },
        comment: {
            label: "Label Go-Live",
            key: "planningGoLiveCommentM1"
        }
    },
    {
        date: {
            label: "Go-Live Milestone 2",
            key: "planningGoLiveMilestone2"
        },
        comment: {
            label: "Label Milestone 2",
            key: "planningGoLiveCommentM2"
        }
    },
    {
        date: {
            label: "Go-Live Milestone 3",
            key: "planningGoLiveMilestone3"
        },
        comment: {
            label: "Label Milestone 3",
            key: "planningGoLiveCommentM3"
        }
    },
    {
        date: {
            label: "Go-Live Milestone 4",
            key: "planningGoLiveMilestone4"
        },
        comment: {
            label: "Label Milestone 4",
            key: "planningGoLiveCommentM4"
        }
    },
    {
        date: {
            label: "Switch-off Milestone 1",
            key: "planningSwitchOffMilestone1"
        },
        comment: {
            label: "Label Milestone 1",
            key: "planningSwitchOffCommentM1"
        }
    },
    {
        date: {
            label: "Switch-off Milestone 2",
            key: "planningSwitchOffMilestone2"
        },
        comment: {
            label: "Label Milestone 2",
            key: "planningSwitchOffCommentM2"
        }
    },
    {
        date: {
            label: "Switch-off Milestone 3",
            key: "planningSwitchOffMilestone3"
        },
        comment: {
            label: "Label Milestone 3",
            key: "planningSwitchOffCommentM3"
        }
    },
    {
        date: {
            label: "(Final) Switch-off",
            key: "planningSwitchOffMilestone4"
        },
        comment: {
            label: "Label Switch-Off",
            key: "planningSwitchOffCommentM4"
        }
    },

]

interface Props {
    app: AppFactSheet;
    isModalOpen: boolean;
    setIsModalOpen: (value: boolean) => void;
    refresh: () => Promise<void>;
    updateApplication: (app: AppFactSheet, formData: MilestonesFormData | undefined) => void;
}

export default function EditMilestonesModal({app, isModalOpen, setIsModalOpen, refresh, updateApplication}: Props) {

    const [submitted, setSubmitted] = useState(false);

    const [errorDateIndex, setErrorDateIndex] = useState(-1);

    const [formData, setFormData] = useState<MilestonesFormData>({
        planningGoLiveMilestone1: app.planningGoLiveMilestone1,
        planningGoLiveMilestone2: app.planningGoLiveMilestone2,
        planningGoLiveMilestone3: app.planningGoLiveMilestone3,
        planningGoLiveMilestone4: app.planningGoLiveMilestone4,
        planningSwitchOffMilestone1: app.planningSwitchOffMilestone1,
        planningSwitchOffMilestone2: app.planningSwitchOffMilestone2,
        planningSwitchOffMilestone3: app.planningSwitchOffMilestone3,
        planningSwitchOffMilestone4: app.planningSwitchOffMilestone4,
        planningGoLiveCommentM1: app.planningGoLiveCommentM1,
        planningGoLiveCommentM2: app.planningGoLiveCommentM2,
        planningGoLiveCommentM3: app.planningGoLiveCommentM3,
        planningGoLiveCommentM4: app.planningGoLiveCommentM4,
        planningSwitchOffCommentM1: app.planningSwitchOffCommentM1,
        planningSwitchOffCommentM2: app.planningSwitchOffCommentM2,
        planningSwitchOffCommentM3: app.planningSwitchOffCommentM3,
        planningSwitchOffCommentM4: app.planningSwitchOffCommentM4,
    });


    const getPhase = (phaseName: string) => app.originalLifecycle ? app.originalLifecycle.phases.filter(p => p.phase === phaseName).length > 0 ?
        app.originalLifecycle?.phases.filter(p => p.phase === phaseName)[0].startDate as string :
        "" : "";


    const originalActive: string = getPhase("active");
    const originalPhaseOut: string = getPhase("phaseOut");

    const validateComments = () => {
        return fields.every(field => {
            const dateKey = field.date.key;
            const commentKey = field.comment.key;
            const dateValue = formData[dateKey];
            const commentValue = formData[commentKey];
            // Wenn ein Datum gesetzt ist, überprüfen, ob auch ein Kommentar vorhanden ist
            return !dateValue || (dateValue && commentValue);
        });
    }

    // Funktion zum Überprüfen der Reihenfolge der Daten
    const validateDates = (formData: MilestonesFormData) => {

        let dates = [formData.planningGoLiveMilestone1,
            formData.planningGoLiveMilestone2,
            formData.planningGoLiveMilestone3,
            formData.planningGoLiveMilestone4,
            formData.planningSwitchOffMilestone1,
            formData.planningSwitchOffMilestone2,
            formData.planningSwitchOffMilestone3,
            formData.planningSwitchOffMilestone4];


        let previousDate = null;

        for (let i = 0; i < dates.length - 1; i++) {

            if (dates[i] !== '') previousDate = dates[i];

            if (previousDate && new Date(previousDate) >= new Date(dates[i + 1])) {
                return i + 1;
            }
        }

        return -1;
    }


    useEffect(() => {
        if (isModalOpen) {
            // console.log("APP", app)
            setSubmitted(false);
            setErrorDateIndex(-1);
            setFormData({
                planningGoLiveMilestone1: app.planningGoLiveMilestone1,
                planningGoLiveMilestone2: app.planningGoLiveMilestone2,
                planningGoLiveMilestone3: app.planningGoLiveMilestone3,
                planningGoLiveMilestone4: app.planningGoLiveMilestone4,
                planningSwitchOffMilestone1: app.planningSwitchOffMilestone1,
                planningSwitchOffMilestone2: app.planningSwitchOffMilestone2,
                planningSwitchOffMilestone3: app.planningSwitchOffMilestone3,
                planningSwitchOffMilestone4: app.planningSwitchOffMilestone4,
                planningGoLiveCommentM1: app.planningGoLiveCommentM1,
                planningGoLiveCommentM2: app.planningGoLiveCommentM2,
                planningGoLiveCommentM3: app.planningGoLiveCommentM3,
                planningGoLiveCommentM4: app.planningGoLiveCommentM4,
                planningSwitchOffCommentM1: app.planningSwitchOffCommentM1,
                planningSwitchOffCommentM2: app.planningSwitchOffCommentM2,
                planningSwitchOffCommentM3: app.planningSwitchOffCommentM3,
                planningSwitchOffCommentM4: app.planningSwitchOffCommentM4,
            })

        }

    }, [isModalOpen]);


    const handleOk = () => {

        setSubmitted(true);

        if (!validateComments()) {
            lx.showToastr("error", "A comment is required for each set date.");
            setSubmitted(false);
            return;
        }



        // ADD FORM VALIDATOR

        const validDates = validateDates(formData);
        setErrorDateIndex(validDates);

        if (validDates !== -1) {
            lx.showToastr("error", "The dates must be in ascending order.");
            setSubmitted(false);
            return;
        }


        const mutation = `mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "${app.id}", patches: $patches, validateOnly: false) {
    factSheet {
      ... on Application {
        rev
        planningGoLiveMilestone1
        planningGoLiveMilestone2
        planningGoLiveMilestone3
        planningGoLiveMilestone4
        planningSwitchOffMilestone1
        planningSwitchOffMilestone2
        planningSwitchOffMilestone3
        planningSwitchOffMilestone4
        planningGoLiveCommentM1
        planningGoLiveCommentM2
        planningGoLiveCommentM3
        planningGoLiveCommentM4
        planningSwitchOffCommentM1
        planningSwitchOffCommentM2
        planningSwitchOffCommentM3
        planningSwitchOffCommentM4
         lifecycle {
          phases {
            phase
            startDate
          }
        }
      }
    }
  }
}
`
        const patches = `{
   "patches":[
      {
         "op":"replace",
         "path":"/planningGoLiveMilestone1",
         "value":"${formData.planningGoLiveMilestone1}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveMilestone2",
         "value":"${formData.planningGoLiveMilestone2}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveMilestone3",
         "value":"${formData.planningGoLiveMilestone3}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveMilestone4",
         "value":"${formData.planningGoLiveMilestone4}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffMilestone1",
         "value":"${formData.planningSwitchOffMilestone1}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffMilestone2",
         "value":"${formData.planningSwitchOffMilestone2}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffMilestone3",
         "value":"${formData.planningSwitchOffMilestone3}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffMilestone4",
         "value":"${formData.planningSwitchOffMilestone4}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveCommentM1",
         "value":"${formData.planningGoLiveCommentM1}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveCommentM2",
         "value":"${formData.planningGoLiveCommentM2}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveCommentM3",
         "value":"${formData.planningGoLiveCommentM3}"
      },
      {
         "op":"replace",
         "path":"/planningGoLiveCommentM4",
         "value":"${formData.planningGoLiveCommentM4}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffCommentM1",
         "value":"${formData.planningSwitchOffCommentM1}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffCommentM2",
         "value":"${formData.planningSwitchOffCommentM2}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffCommentM3",
         "value":"${formData.planningSwitchOffCommentM3}"
      },
      {
         "op":"replace",
         "path":"/planningSwitchOffCommentM4",
         "value":"${formData.planningSwitchOffCommentM4}"
      }
      
      ${formData.planningGoLiveMilestone1 !== app.planningGoLiveMilestone1 || formData.planningGoLiveMilestone1 !== originalActive ?
            `,{
         "op": "${formData.planningGoLiveMilestone1.length === 0 ? "remove" : "replace"}",
         "path": "/lifecycle/active",
         "value": "${formData.planningGoLiveMilestone1}"
      }` : ''}
      
      ${formData.planningSwitchOffMilestone4 !== app.planningSwitchOffMilestone4 || formData.planningSwitchOffMilestone4 !== originalPhaseOut ? `
      ,{
         "op": "${formData.planningSwitchOffMilestone4.length === 0 ? "remove" : "replace"}",
        "path": "/lifecycle/phaseOut",
        "value": "${formData.planningSwitchOffMilestone4}" 
     }
      ` : ''}
   ]
}`

        setIsModalOpen(false);

        lx.showSpinner();
        updateApplication(app, formData);

        lx.executeGraphQL(mutation, patches).then((result) => {
            console.log(result)
            lx.showToastr("success", "Changes saved successfully");
            lx.hideSpinner();
        }).catch(error => {
            lx.showToastr("error", `Error while saving changes. ${error.message}. Please try it again`)
            lx.hideSpinner();
            console.log("ERROR", error)
            updateApplication(app, undefined);
        })
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

/*
    const getDefaultValue = (key:string, value:string, dateValue:string, idx:number) => {

        if(dateValue.length > 0 && idx === 0 && value.length === 0) {
            const defaultValue = "First Go-Live";
            setFormData({...formData, [key]:defaultValue})
            return defaultValue;
        }

        if(dateValue.length > 0 && idx === 7 && value.length === 0){
            const defaultValue = "Last Switch-Off";
            setFormData({...formData, [key]:defaultValue})
            return defaultValue;
        }
        else return value;

    }
*/

    const setDefaultValues = (dateKey:string, value: string ) => {

        if(value.length > 0 && dateKey === "planningGoLiveMilestone1" && formData.planningGoLiveCommentM1.length === 0){
            setFormData(prev => ({...prev, ["planningGoLiveCommentM1"]: "First Go-Live" }));
        }

        if(value.length > 0 && dateKey === "planningSwitchOffMilestone4" && formData.planningSwitchOffCommentM4.length === 0){
            setFormData(prev => ({...prev, ["planningSwitchOffCommentM4"]: "Last Switch-off" }));
        }

    }

    const showError = (date:string, comment:string) => {


        if(date.length > 0 && comment.length === 0) return "error"


        return undefined;
    }

    return (
        <Modal width={700} title={`Edit Milestones - ${app.displayName}`} okText={"Apply"} cancelText={"Cancel"}
               open={isModalOpen}
               okButtonProps={{
                   style: {
                       backgroundColor: "#b80718"
                   },
                   disabled: submitted
               }} onOk={handleOk} onCancel={handleCancel}>


            {fields.map((f, index) => (
                <Row gutter={16} key={index} style={{marginTop: "16px"}}>
                    <Col span={8}>
                        {f.date.label}
                    </Col>
                    <Col span={6}>
                        <DatePicker status={errorDateIndex === index ? "error" : undefined} format="DD.MM.YYYY"
                                    placeholder={f.date.label}
                                    onChange={(date, dateString) => {setFormData((prev =>({
                                        ...prev,
                                        [f.date.key]: dateString.length > 0 ? dayjs(dateString as string, "DD.MM.YYYY").format("YYYY-MM-DD") : ""
                                    })));

                                    setDefaultValues(f.date.key,dateString as string);
                                    }}
                                    value={formData[f.date.key] ? dayjs(formData[f.date.key], "YYYY-MM-DD") : undefined}/>
                    </Col>
                    <Col span={8}>
                        <Input placeholder={f.comment.label} value={formData[f.comment.key]}
                               status={showError(formData[f.date.key], formData[f.comment.key])}
                               onChange={(e) => setFormData({...formData, [f.comment.key]: e.target.value})}/>
                    </Col>
                </Row>
            ))}
        </Modal>
    );
}