import React from 'react'
import './RoadmapLegend.css'

interface RoadmapLegendProps {
  years: number[]
  yearWidth: number

}

const RoadmapLegend = ({ years, yearWidth }: RoadmapLegendProps) => {
  let leftPos = 40
  return (
    <>
      <div id="roadmap-legend">
        {years.map((year, id) => {
          leftPos += yearWidth
          return (
            <div className="roadmap-item-wrapper" key={`${year}-legend-wrapper`} style={{ width: `${yearWidth}px` }}>
              <div className="roadmap-item" key={`${year}-legend`}>
                {year}
              </div>
            </div>
          )
        })}
      </div>
    </>
  )
}

export default RoadmapLegend
