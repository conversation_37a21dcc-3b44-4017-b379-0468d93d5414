import { Input, Space, Switch } from 'antd'
import React from 'react'

interface Props {
  searchText: string
  setSearchText: React.Dispatch<React.SetStateAction<string>>
  showAll: boolean
  setShowAll: React.Dispatch<React.SetStateAction<boolean>>
}

const Filters = ({ searchText, setSearchText, showAll, setShowAll }: Props) => {
  const { Search } = Input

  return (
    <Space>
      <Search
        allowClear
        value={searchText}
        placeholder="Search..."
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchText(e.target.value)}

      />
      <span>
        Apps ohne Business Criticality anzeigen:
        <Switch
          size="small"
          onChange={(value: boolean) => setShowAll(value)}
          checked={showAll}
        />
      </span>

    </Space>
  )
}

export default Filters
