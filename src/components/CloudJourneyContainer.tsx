import type { LoadDataResult } from '@app/model'
import SourcingChart from '@components/SourcingChart'
import SourcingPie from '@components/SourcingPie'
import { prepareSourcingPieData } from '@utils/parser'
import { Card, Col, Row } from 'antd'
import React from 'react'

interface Props {
  data: LoadDataResult
  sourcing: any[]
}

export default function CloudJourneyContainer({ data, sourcing }: Props) {
  console.log('SOURCING', sourcing)
  const sourcingData = prepareSourcingPieData(data, sourcing)
  console.log('SOURCING DATA', sourcingData)

  return (
    <Row gutter={12}>
      <Col span={24} style={{ marginTop: 20 }}>
        <Card title="Applications by Sourcing - Absoulte numbers">
          <SourcingChart data={data} isRelative={false} />
        </Card>
      </Col>
      <Col span={24} style={{ marginTop: 20 }}>
        <Card title="Applications by Sourcing - Relative numbers">
          <SourcingChart data={data} isRelative maxYAxis={100} />
        </Card>
      </Col>

      <Col lg={12} sm={24} style={{ marginTop: 20 }}>
        <Card title="Live View">
          <SourcingPie data={sourcingData.liveData} />
        </Card>
      </Col>
      <Col lg={12} sm={24} style={{ marginTop: 20 }}>
        <Card title={`Q1/${data.nextYearData.date.getFullYear()}`}>
          <SourcingPie data={sourcingData.nextYearData} />
        </Card>
      </Col>
      <Col lg={12} sm={24} style={{ marginTop: 20 }}>
        <Card title={`Q1/${data.secondYearData.date.getFullYear()}`}>
          <SourcingPie data={sourcingData.secondYearData} />
        </Card>
      </Col>
      <Col lg={12} sm={24} style={{ marginTop: 20 }}>
        <Card title={`Q1/${data.thirdYearData.date.getFullYear()}`}>
          <SourcingPie data={sourcingData.thirdYearData} />
        </Card>
      </Col>
    </Row>
  )
}
