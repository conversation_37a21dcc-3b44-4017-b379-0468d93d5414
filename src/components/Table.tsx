import type { ApplicationModel } from '../utils/parser'
import { ExportOutlined } from '@ant-design/icons'
import { Badge, Button, Col, Row, Space, Table, Tag, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import useWindowDimensions from '../hooks/useWindowDimensions'
import { exportData } from '../utils'
import { BusinessCriticalities, Current_Sourcings, SORT_ORDER } from '../utils/constants'
import Filters from './Filters'
import { RessortFilter } from './RessortFilter'

interface Props {
  data: ApplicationModel[]
  searchText: string
  setSearchText: React.Dispatch<React.SetStateAction<string>>
  ressortFilterValue: string
  setRessortFilterValue: React.Dispatch<React.SetStateAction<string>>
  showAll: boolean
  setShowAll: React.Dispatch<React.SetStateAction<boolean>>
}

const DataTable = ({
  data,
  searchText,
  setSearchText,
  ressortFilterValue,
  setRessortFilterValue,
  showAll,
  setShowAll
}: Props) => {
  const [filteredData, setFilteredData] = useState<ApplicationModel[]>(data)
  const [ressortFilterData, setRessortFilterData] = useState<ApplicationModel[]>(data)
  const { Column } = Table

  const { height } = useWindowDimensions()

  useEffect(() => {
    let preparedData = data

    if (!showAll) {
      preparedData = preparedData.filter(a => a.businessCriticality !== '')
    }

    // text search
    if (searchText.length > 0) {
      preparedData = preparedData.filter(a => a.name.toLowerCase().includes(searchText.toLowerCase()))
    }

    // corp units

    setRessortFilterData(preparedData)

    if (ressortFilterValue != 'all') {
      preparedData = preparedData.filter(a => a.ITRessort.name === ressortFilterValue)
    }

    setFilteredData(preparedData.sort((a, b) => a.name.localeCompare(b.name)))
  }, [ressortFilterValue, data, showAll, searchText])

  return (
    <>
      <Row>
        <Col span={24}>
          <Filters
            searchText={searchText}
            setSearchText={setSearchText}
            showAll={showAll}
            setShowAll={setShowAll}
          />
        </Col>
        <Col span={24}>
          <br />
          <RessortFilter
            ressortFilterValue={ressortFilterValue}
            setRessortFilterValue={setRessortFilterValue}
            allData={ressortFilterData}
          />
        </Col>
        <Col
          span={24}
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            paddingRight: '10px'
          }}
        >
          <Space>
            <Button
              icon={<ExportOutlined />}
              size="small"
              onClick={
                () => exportData(filteredData, true)
              }
            >
              export XLSX
            </Button>
            <Button
              icon={<ExportOutlined />}
              size="small"
              onClick={
                () => exportData(filteredData, false)
              }
            >
              export CSV
            </Button>
          </Space>
        </Col>
      </Row>

      <Row>
        <Col
          span={24}
          style={{
            paddingTop: '10px',
            paddingRight: '10px'
          }}
        >
          <Table
            dataSource={filteredData}
            rowKey="id"
            pagination={false}

            size="small"
            virtual
            scroll={{ y: height - 280, x: 'auto' }}
            childrenColumnName="applications"
          >
            <Column
              title="Applikation"
              fixed="left"
              dataIndex="name"
              key="name"
              width={300}
              render={(value: string, row: ApplicationModel) => {
                return (
                  <a onClick={() => lx.openLink(`/factsheet/` + `Application` + `/${row.id}`)}>
                    {value}
                  </a>
                )
              }}
            />
            <Column title="Level" dataIndex="level" key="level" width={55} />
            <Column
              title="Lifecycle"
              dataIndex="lifeCycle"
              key="lifeCycle"
              width={100}
              render={(record: string, row: ApplicationModel) => {
                if (row.lifeCycle === 'active') {
                  return <Badge status="success" text="Active" />
                }
                else if (row.lifeCycle === 'endOfLife') {
                  return <Badge status="error" text="End of life" />
                }
                else if (row.lifeCycle === 'phaseIn') {
                  return <Badge status="default" text="Phase in" />
                }
                else if (row.lifeCycle === 'phaseOut') {
                  return <Badge status="warning" text="Phase out" />
                }
                else {
                  return record
                }
              }}
            />
            <Column
              title="Description"
              dataIndex="description"
              key="description"
              width={130}
              render={(value: string) => {
                if (value) {
                  if (value.length > 20) {
                    return (
                      <Tooltip placement="top" title={value}>
                        {`${value.slice(0, 20)}...`}
                      </Tooltip>
                    )
                  }
                  else {
                    return value
                  }
                }
                return ''
              }}
              ellipsis={{ showTitle: false }}
            />
            <Column
              title="Domain"
              dataIndex="domain"
              key="domain"
              width={170}
            />
            <Column
              title="Plattform"
              dataIndex="plattform"
              key="plattform"
              width={80}
            />
            <Column
              title="Hosting"
              dataIndex="hosting"
              key="hosting"
              width={120}
              render={(hosting: string) => {
                const index = Current_Sourcings.findIndex(
                  element => hosting == element.value
                )

                if (index == -1) {
                  return 'Sourcing not found'
                }
                else if (Current_Sourcings[index].text == 'No Sourcing') {
                  return ' '
                }
                else {
                  return Current_Sourcings[index].text
                }
              }}
            />
            <Column
              title="3rd Party"
              dataIndex="thirdParty"
              key="thirdParty"
              width={100}
              render={(record: string, row: ApplicationModel) => {
                if (record === 'thirdParty') {
                  return '3rd Party SW'
                }
                if (record === 'individual') {
                  return 'Individual Software'
                }
                return record
              }}
            />
            <Column
              title="Business Criticallity"
              dataIndex="businessCriticality"
              key="businessCriticality"
              width={150}
              sorter={(a: ApplicationModel, b: ApplicationModel) => SORT_ORDER.indexOf(a.businessCriticality) - SORT_ORDER.indexOf(b.businessCriticality)}
              render={(value: string) => {
                let text: string = ''
                if (value === BusinessCriticalities.MISSION_CRITICAL.value) {
                  text = BusinessCriticalities.MISSION_CRITICAL.label
                  return (<Tag color={BusinessCriticalities.MISSION_CRITICAL.color}>{text}</Tag>)
                }
                else if (value === BusinessCriticalities.BUSINESS_CRITICAL.value) {
                  text = BusinessCriticalities.BUSINESS_CRITICAL.label
                  return (<Tag color={BusinessCriticalities.BUSINESS_CRITICAL.color}>{text}</Tag>)
                }
                else if (value === BusinessCriticalities.BUSINESS_OPERATIONAL.value) {
                  text = BusinessCriticalities.BUSINESS_OPERATIONAL.label
                  return (<Tag color={BusinessCriticalities.BUSINESS_OPERATIONAL.color}>{text}</Tag>)
                }
                else if (value === BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.value) {
                  text = BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.label
                  return (
                    <Tag color={BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.color}>{text}</Tag>)
                }
                return text
              }}
            />
            <Column
              title="Description"
              dataIndex="businessCriticalityDescription"
              key="businessCriticalityDescription"
              width={130}
              render={(value: string) => {
                if (value) {
                  if (value.length > 25) {
                    return (
                      <Tooltip placement="top" title={value}>
                        {`${value.slice(0, 25)}...`}
                      </Tooltip>
                    )
                  }
                  else {
                    return value
                  }
                }
                return ''
              }}
              ellipsis={{ showTitle: false }}
            />
          </Table>
        </Col>
      </Row>
    </>
  )
}

export default DataTable
