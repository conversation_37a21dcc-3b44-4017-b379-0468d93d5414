import type { ApplicationModel } from '../../model'
import { Col, Row, Skeleton } from 'antd'
import React, { useEffect, useState } from 'react'
import * as XLSX from 'xlsx'
import { parseDateForExport } from '../../parser'
import ExportMenu from '../ExportMenu'
import Filters from './../Filters'
import DataTable from './DataTable'

interface Props {
  data: ApplicationModel[]
  rawData: any[]
  user: lxr.ReportUser | undefined
  userMode: boolean
  updateUserMode: (checked: boolean, subType: lxr.SubscriptionType) => void
  subscriptionType: lxr.SubscriptionType
  loading: boolean
}

export default function LifeCycleViewContainer({
  data,
  rawData,
  user,
  userMode,
  updateUserMode,
  subscriptionType,
  loading
}: Props) {
  const [filteredApplications, setFilteredApplications] = useState<ApplicationModel[]>([])
  const [searchText, setSearchText] = useState('')
  const [corpUnits, setCorpUnits] = useState(['CF', 'CH'])

  const exportData = (xlsx: boolean) => {
    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(
      parseDateForExport(rawData, filteredApplications)
    )

    XLSX.utils.book_append_sheet(wb, ws_application, 'Application Lifecycle View')
    XLSX.writeFile(wb, xlsx ? 'export.xlsx' : 'export.csv')
  }

  useEffect(() => {
    let filtered = [...data]

    if (searchText.length > 0) {
      filtered = filtered.filter(a =>
        a.fullName.toLowerCase().includes(searchText.toLowerCase())
      )
    }

    if (corpUnits.length > 0) {
      filtered = filtered.filter(a => corpUnits.includes(a.corpUnit))
    }

    setFilteredApplications(filtered.sort((a, b) => a.fullName.toLowerCase().localeCompare(b.fullName.toLowerCase())))
  }, [data, searchText, corpUnits])

  return (
    <>
      <Row>
        <Col md={24} lg={18}>
          <Filters
            searchLabel="Application Search..."
            corpUnits={corpUnits}
            setCorpUnits={setCorpUnits}
            setSearchText={setSearchText}
            user={user}
            userMode={userMode}
            updateUserMode={updateUserMode}
            subscriptionType={subscriptionType}
          />
        </Col>
        <Col md={24} lg={6} style={{ marginTop: '8px' }}>
          <ExportMenu exportData={(value: boolean) => exportData(value)} />
        </Col>
      </Row>
      <br />
      <Row>
        <Col span={24}>
          {loading ? <Skeleton active /> : <DataTable data={filteredApplications} />}
        </Col>
      </Row>
    </>
  )
}
