import type { LifecycleYear } from '../../model'
import { Row, Space } from 'antd'
import React from 'react'
import CustomProgress from './CustomProgress'

interface Props {
  data: LifecycleYear[]
}

export default function LifecycleView({ data }: Props) {
  const today = new Date()
  const quarter = Math.floor((today.getMonth() + 3) / 3)

  return (
    <Row style={{ minWidth: '440px' }}>
      {data.map((year) => {
        const currentYear = today.getFullYear() === year.year

        return (
          <Space
            key={`${year.year}`}
            direction="vertical"
            size={0}
            style={{ display: 'flex', padding: '1px', marginRight: '6px' }}
          >
            <CustomProgress year={year} currentYear={currentYear} currentQuarter={quarter} />

            <div style={{ display: 'flex', marginTop: '2px' }}>
              <small style={currentYear
                ? {
                    margin: 'auto',
                    fontWeight: 'bold'
                  }
                : { margin: 'auto' }}
              >
                {year.year}
              </small>
            </div>
          </Space>
        )
      })}
    </Row>
  )
}
