import {Col, Popover, Row, Table, Typography} from "antd";
import {ExclamationCircleTwoTone} from "@ant-design/icons";
import React from "react";
import {AppFactSheet, Lifecycle} from "@app/models/data";
import dayjs from 'dayjs';

interface Props {
    app: AppFactSheet;
}

export default function LifecycleMatchPopover({app}: Props) {

    const {Text} = Typography;


    const displayedPhases = app.validLifecycle ? [
        {
            title: "Go-Live",
            dataIndex: "active",
            key: "active",
        },
        {
            title: "Switch Off",
            dataIndex: "phaseOut",
            key: "phaseOut",
        }
    ] : [
        {
            title: "Plan",
            dataIndex: "plan",
            key: "plan"
        },
        {
            title: "Phase in",
            dataIndex: "phaseIn",
            key: "phaseIn"
        },
        {
            title: "Go-Live",
            dataIndex: "active",
            key: "active"
        },
        {
            title: "Switch Off",
            dataIndex: "phaseOut",
            key: "phaseOut"
        },
        {
            title: "Decommissioned",
            dataIndex: "endOfLife",
            key: "endOfLife"
        }];


    const columns = [{
        title: "Source",
        dataIndex: "source",
        key: "source",
    }, ...displayedPhases];


    const getDate = (lifecycle: Lifecycle | undefined, phase: string) => lifecycle ? lifecycle.phases.filter(p => p.phase === phase).length > 0 ?
        `${lifecycle.phases.filter(p => p.phase === phase)[0].startDate}` : "" : "";


    const formatDate = (date: string) => date ? dayjs(date).format("DD.MM.YYYY") : "";


    const lifecycleActive = getDate(app.originalLifecycle, "active");
    const lifecyclePhaseOut = getDate(app.originalLifecycle, "phaseOut");


    const displayColorMilestone = (value: string) => value ? <Text type="success">{value}</Text> :
        <Text type="danger">{value}</Text>
    const displayColorLifecycle = (date: string, milestone: boolean, equal: boolean) => (milestone || equal) ?
        <Text type="success">{date}</Text> : <Text type="danger">{date}</Text>


    const data = [{
        source: "Milestones",
        active: displayColorMilestone(formatDate(app.planningGoLiveMilestone1)),
        phaseOut: displayColorMilestone(formatDate(app.planningSwitchOffMilestone4))
    },
        {
            source: "Lifecycle",
            active: displayColorLifecycle(formatDate(lifecycleActive), app.planningGoLiveMilestone1.length === 0, app.planningGoLiveMilestone1 === lifecycleActive),
            phaseOut: displayColorLifecycle(formatDate(lifecyclePhaseOut), app.planningSwitchOffMilestone4.length === 0, app.planningSwitchOffMilestone4 === lifecyclePhaseOut),

            plan: displayColorMilestone(formatDate(getDate(app.originalLifecycle,"plan"))),
            phaseIn: displayColorMilestone(formatDate(getDate(app.originalLifecycle,"phaseIn"))),
            endOfLife: displayColorMilestone(formatDate(getDate(app.originalLifecycle,"endOfLife")))

        }];

    const popoverContent = <Row style={{maxWidth: app.validLifecycle ?"350px" : "550px"}}>
        <Col span={24}>
            {app.validLifecycle ? <Text>Lifecycle doesn't match with the milestones. The green value is displayed in the timeline. </Text> :
                <Text type="danger">Please update the life cycle / milestones so that it is consistent!</Text>
            }

        </Col>
        <Col span={24}>
            <Table size="small"
                   rowKey="source"
                   pagination={false}
                   dataSource={data} columns={columns}/>
        </Col>
    </Row>

    return (
        <Popover content={popoverContent} title="Matching error">
            <ExclamationCircleTwoTone style={{marginRight: "4px"}} twoToneColor="#ffa940"/>
        </Popover>
    );

}