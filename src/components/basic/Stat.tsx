import type { Statistic } from '@app/model'
import { Card } from 'antd'
import React from 'react'

interface Props {
  stat: Statistic
}

export default function Stat({ stat }: Props) {
  const customStatistic = (title: string, value: string, color: string | undefined, suffix?: string) => (
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <div style={{ fontWeight: 'bold', marginRight: '10px' }}>{title}</div>
      <div style={{ color, fontSize: '1.2em' }}>
        <span style={{ fontWeight: 'bold', fontSize: '18px' }}>
          {value}
          {suffix && <span>{suffix}</span>}
        </span>
      </div>
    </div>
  )

  return (
    <Card bordered={false} size="small" style={{ minWidth: '160px' }}>
      {customStatistic(stat.label, stat.value || 'n/a', stat.color, stat.suffix)}
    </Card>
  )
}
