import { Tag, Tooltip } from 'antd'
import React from 'react'

interface Props {
  text: string
  limit?: number
}

const ValueTag = ({ text, limit }: Props) => {
  const textLength = limit || 18
  const shortenedText = text.length > textLength ? `${text.slice(0, textLength)}...` : text

  return (
    <Tooltip title={text}>
      <Tag style={{ cursor: 'context-menu' }}>{shortenedText}</Tag>
    </Tooltip>
  )
}
export default ValueTag
