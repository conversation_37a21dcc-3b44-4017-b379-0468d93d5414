import type { PieModel } from '@app/model'
import React from 'react'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from 'recharts'

interface Props {
  data: PieModel[]
  size?: number
}

export default function SourcingPie({ data, size = 250 }: Props) {
  const colorMap: Record<string, string> = {
    'Datacenter': '#4C628F',
    'External Hosting': '#C7C4C4',
    'Cloud': '#4CC8F4',
    'Replacement': '#ffc069'
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #999',
          padding: '10px',
          borderRadius: '4px',
          boxShadow: '0 2px 5px rgba(0,0,0,0.15)'
        }}
        >
          <p style={{ margin: 0 }}><strong>{payload[0].name}</strong></p>
          <p style={{ margin: 0 }}>
            Value:
            {payload[0].value}
          </p>
        </div>
      )
    }
    return null
  }

  const outerRadius = Math.min(size * 0.3, 90)

  if (!data || data.length === 0) {
    return (
      <div style={{
        width: size,
        height: size,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999',
        fontSize: '12px'
      }}
      >
        No data available
      </div>
    )
  }

  return (
    <div style={{ width: 500, height: 200 }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine
            outerRadius={outerRadius}
            fill="#8884d8"
            dataKey="value"
            nameKey="type"
            label={({ name, percent, value, x, y }) => {
              const displayText = `${(percent * 100).toFixed(1)}% | ${value}`
              return (
                <text
                  x={x}
                  y={y}
                  fill="#333"
                  textAnchor="start"
                  dominantBaseline="central"
                  fontSize={10}
                >
                  {displayText}
                </text>
              )
            }}
          >
            {data.map((entry, index) => {
              const color = entry.color || colorMap[entry.type] || '#8884d8'
              return (
                <Cell
                  key={`cell-${index}`}
                  fill={color}
                />
              )
            })}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend
            layout="vertical"
            align="right"
            verticalAlign="middle"
            iconSize={size < 220 ? 8 : 10}
            wrapperStyle={{
              fontSize: size < 220 ? 10 : 12,
              paddingRight: 10
            }}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
