import type { FoldedClusters } from '@app/models/data'
// Import Typescript interfaces
import type { AppClusters, AppFactSheet } from '@app/models/data'
import type { TimelineDates } from '../models/timeline.models'
import React from 'react'
import AppClusterTimeline from './AppClusterTimeline'
import AppTimeline from './AppTimeline'

import AxisLines from './AxisLines'
import './Timeline.css'

interface TimelineProps {
  apps: AppFactSheet[] | AppClusters
  timelineDates: TimelineDates
  yearWidth: number
  zoomLevel: number
  showLabels: any
  todayMarker: any
  years: number[]
  clusterBy?: string
  allTags: any
  foldedClusters: FoldedClusters
  openModal: (app: AppFactSheet) => void
}

export default function Timeline({ apps, timelineDates, yearWidth, zoomLevel, showLabels, todayMarker, years, clusterBy, foldedClusters, openModal }: TimelineProps): JSX.Element {
  let appsTimelines = null
  if (clusterBy) {
    appsTimelines = (
      <>
        {
          Object.entries(apps).map(([clusterName, cluster]) => {
            return (
              <AppClusterTimeline
                key={clusterBy + timelineDates.startDate + timelineDates.endDate + yearWidth + zoomLevel + clusterName}
                cluster={cluster}
                timelineDates={timelineDates}
                yearWidth={yearWidth}
                zoomLevel={zoomLevel}
                showLabels={showLabels}
                years={years}
                folded={foldedClusters[clusterName]}
                openModal={openModal}
              />
            )
          })
        }
      </>
    )
  }
  else {
    appsTimelines = (
      <>
        {
          (apps as AppFactSheet[]).map((app: AppFactSheet) => {
            return (
              <AppTimeline
                key={(timelineDates.startDate as string | Date) + (timelineDates.endDate as string) + yearWidth + zoomLevel + app.id}
                app={app}
                timelineDates={timelineDates}
                yearWidth={yearWidth}
                zoomLevel={zoomLevel}
                showLabels={showLabels}
                years={years}
                openModal={openModal}
              />
            )
          })
        }
      </>
    )
  }

  if (apps) {
    return (
      <div id="timeline">
        <AxisLines
          years={years}
          yearWidth={yearWidth * zoomLevel}
          yearsLength={years.length}
        />
        <>
          <div id="today-marker" style={{ marginLeft: `${todayMarker.startDate * zoomLevel}px` }}>
            <div id="today-marker-line"></div>
            <div id="today-marker-point"></div>
          </div>

          {appsTimelines}
        </>
      </div>
    )
  }
  else {
    return <></>
  }
}
