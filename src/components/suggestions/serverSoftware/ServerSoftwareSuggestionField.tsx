import type { FormInstance } from 'antd'
import type { DesktopSoftwareAppData, Value } from '../../../model'
import { Select, Skeleton } from 'antd'
import React, { useEffect, useState } from 'react'
import { useLeaniX } from '../../../hooks/leanix'
import ServerSoftwareSuggestions from './ServerSoftwareSuggestions'

interface Props {
  form: FormInstance
}

export default function ServerSoftwareSuggestionField({ form, ...props }: Props) {
  const { getAppDataById, getApplications } = useLeaniX()
  const [suggestion, setSuggestion] = useState<DesktopSoftwareAppData | undefined>(undefined)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [applications, setApplications] = useState<Value[]>([])
  const [loading, setLoading] = useState(true)

  const loadAppData = async (appId: string) => {
    const data: DesktopSoftwareAppData | undefined = await getAppDataById(appId)
    setSuggestion(data)
    setShowSuggestions(true)
  }

  const applySuggestions = (data: DesktopSoftwareAppData) => {
    if (data.description.length > 0) {
      form.setFieldValue('description', data.description)
    }

    if (data.applicationsOwners.length > 0) {
      form.setFieldValue('applicationOwners', [...data.applicationsOwners.map(d => (d.value))])
    }

    setShowSuggestions(false)
  }

  useEffect(() => {
    const load = async () => {
      const apps = await getApplications()
      if (apps) { setApplications(apps) }
    }
    load().then(() => setLoading(false))
  }, [])

  return (
    <>
      <ServerSoftwareSuggestions
        setIsOpen={setShowSuggestions}
        open={showSuggestions}
        handleOk={applySuggestions}
        appData={suggestion}
      />
      {loading
        ? <Skeleton.Input block active />
        : (
            <Select
              showSearch
              {...props}
              placeholder="Search to Select"
              allowClear
              onSelect={value => loadAppData(value)}
              optionFilterProp="children"
              filterOption={(input, option) => (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
              options={applications}
            />
          )}
    </>
  )
}
