import type { Application } from '@app/models'
import { Col, Flex, Row, Skeleton } from 'antd'
import React, {JSX, useEffect, useState} from 'react'
import ExportButton from './../../components/core/ExportButton'
import { AT_LEAST_ONE_FILTER } from './../../constants'
import { exportGroupApplications, sortRecursively } from './../../helper'
import GroupApplicationsFilter from './GroupApplicationsFilter'
import GroupApplicationsTable from './GroupApplicationsTable'

interface Props {
  data: Application[]
  loading: boolean
}

export default function GroupApplicationsContainer({ data, loading }: Props): JSX.Element {
  const [searchText, setSearchText] = React.useState<string>('')
  const [filteredApplications, setFilteredApplications] = React.useState<Application[]>([])
  const [corpUnits, setCorpUnits] = useState(['CF', 'CH'])
  const [atLeastOneBusinessArea, setAtLeastOneBusinessArea] = React.useState(true)

  useEffect(() => {
    let filteredApplications = [...data]

    if (searchText && searchText.length > 0) {
      filteredApplications = (data.filter(a => a.name.toLowerCase().includes(searchText.toLowerCase())))
    }

    if (corpUnits && corpUnits.length > 0) {
      filteredApplications = filteredApplications.filter(a => corpUnits.includes(a.corpUnit))
    }

    if (atLeastOneBusinessArea) {
      filteredApplications = filteredApplications.filter(a =>
        a.businessAreas.some(b => AT_LEAST_ONE_FILTER.includes(b.value))
      )
    }

    // Sortierung hinzufügen
    filteredApplications = sortRecursively(filteredApplications)

    setFilteredApplications(filteredApplications)
  }, [searchText, data, corpUnits, atLeastOneBusinessArea])

  return (
    <>
      <Row gutter={16}>
        <Col span={24}>
          <Flex justify="space-between" align="baseline">
            <GroupApplicationsFilter
              searchText={searchText}
              setSearchText={setSearchText}
              corpUnits={corpUnits}
              setCorpUnits={setCorpUnits}
              atLeastOneBusinessArea={atLeastOneBusinessArea}
              setAtLeastOneBusinessArea={setAtLeastOneBusinessArea}
            />

            <ExportButton download={xlsx => exportGroupApplications(xlsx, filteredApplications)} />
          </Flex>
        </Col>
        <Col span={24} style={{ paddingTop: '10px' }}>
          {loading
            ? <Skeleton active />
            : <GroupApplicationsTable data={filteredApplications} />}
        </Col>
      </Row>

    </>
  )
}
