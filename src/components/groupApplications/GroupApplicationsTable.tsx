import type { BusinessAreaFilter, ValueFilter } from '@app/models'
import type { BadgeProps } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import type { FilterValue, SorterResult } from 'antd/es/table/interface'
import { CheckCircleFilled, CloseCircleOutlined } from '@ant-design/icons'
import { Badge, Flex, Table } from 'antd'
import React from 'react'
import { BUSINESS_AREAS } from './../../constants'
import useWindowDimensions from './../../hooks/useWindowDimensions'

interface Value {
  label: string
  value: string
}

interface Application {
  id: string
  name: string
  level: number
  lifecycle: string
  domain: Value | undefined
  businessAreas: Value[]
  children: Application[] | undefined
}

interface GroupApplicationsTableProps {
  data: Application[]
}

export default function GroupApplicationsTable({ data }: GroupApplicationsTableProps) {
  const { height } = useWindowDimensions()

  // Custom sorter für Business Areas
  const businessAreaSorter = (a: Application, b: Application, country: string): number => {
    const aHasArea = a.businessAreas?.some((b: Value) => b.label.includes(country))
    const bHasArea = b.businessAreas?.some((b: Value) => b.label.includes(country))
    return aHasArea === bHasArea ? 0 : aHasArea ? 1 : -1
  }

  // Custom filter für Business Areas
  const businessAreaFilter: BusinessAreaFilter[] = [
    { text: 'Yes', value: true },
    { text: 'No', value: false }
  ]

  const businessAreaColumns: ColumnsType<Application> = BUSINESS_AREAS.map(country => ({
    title: country,
    dataIndex: country.toLowerCase(),
    key: country.toLowerCase(),
    width: 72,
    filters: businessAreaFilter,
    onFilter: (value: boolean | React.Key, record: Application): boolean => {
      const hasArea = record.businessAreas?.some((b: Value) => b.label.includes(country))
      return hasArea === value
    },
    sorter: (a: Application, b: Application) => businessAreaSorter(a, b, country),
    render: (_: unknown, record: Application) => {
      if (record.businessAreas?.some((b: Value) => b.label?.includes(country))) {
        return (<Flex justify="center"><CheckCircleFilled style={{ color: 'green', fontSize: '18px' }} /></Flex>)
      }
      return (<Flex justify="center"><CloseCircleOutlined style={{ color: 'gray', fontSize: '18px' }} /></Flex>)
    }
  }))

  const columns: ColumnsType<Application> = [
    {
      title: 'Domain',
      dataIndex: ['domain', 'label'],
      width: 200,
      key: 'domain.label',
      sorter: (a: Application, b: Application) =>
        (a.domain?.label || '').localeCompare(b.domain?.label || ''),
      filters: Array.from(new Set(data.map(item => item.domain?.label)))
        .filter((label): label is string => Boolean(label))
        .map((domain): ValueFilter => ({ text: domain, value: domain })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.domain?.label === value,
      render: (_: unknown, record: Application) =>
        (<span>{record.domain?.label}</span>)
    },
    {
      title: 'Application',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      sorter: (a: Application, b: Application) =>
        a.name.localeCompare(b.name),
      filters: Array.from(new Set(data.map(item => item.name)))
        .map((name): ValueFilter => ({ text: name, value: name })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.name === value,
      render: (_: unknown, record: Application) => (
        <a
          onClick={() => lx.openLink(`/factsheet/Application/${record.id}`)}
          target="_blank"
        >
          {record.name}
        </a>
      )
    },
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      width: 90,
      sorter: (a: Application, b: Application) =>
        a.level - b.level,
      filters: Array.from(new Set(data.map(item => item.level)))
        .map((level): ValueFilter => ({
          text: level.toString(),
          value: level.toString()
        })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.level.toString() === value.toString()
    },
    {
      width: 180,
      title: 'Lifecycle',
      dataIndex: 'lifecycle',
      key: 'lifecycle',
      sorter: (a: Application, b: Application) =>
        a.lifecycle.localeCompare(b.lifecycle),
      filters: Array.from(new Set(data.map(item => item.lifecycle)))
        .map((lifecycle): ValueFilter => ({ text: lx.translateFieldValue('Application', 'lifecycle', lifecycle), value: lifecycle })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.lifecycle === value,
      render: (text: string, record: Application) => {
        const statusMap: Record<string, { status: BadgeProps['status'], text: string }> = {
          active: {
            status: 'success',
            text: lx.translateFieldValue('Application', 'lifecycle', 'active')
          },
          endOfLife: {
            status: 'error',
            text: lx.translateFieldValue('Application', 'lifecycle', 'endOfLife')
          },
          phaseIn: {
            status: 'default',
            text: lx.translateFieldValue('Application', 'lifecycle', 'phaseIn')
          },
          phaseOut: {
            status: 'warning',
            text: lx.translateFieldValue('Application', 'lifecycle', 'phaseOut')
          },
          plan: {
            status: 'default',
            text: lx.translateFieldValue('Application', 'lifecycle', 'plan')
          }
        }

        const config = statusMap[record.lifecycle]
        if (config) {
          return <Badge status={config.status} text={config.text} />
        }
        return text
      }
    }
  ]

  const onChange = (
    pagination: any,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<Application> | SorterResult<Application>[]
  ): void => {
    console.log('Table parameters:', { filters, sorter })
  }

  return (
    <>
      <Table<Application>
        rowKey="id"
        size="small"
        virtual
        bordered
        scroll={{ y: height - 200, x: 'auto' }}
        columns={[...columns, ...businessAreaColumns]}
        dataSource={data}
        onChange={onChange}
        pagination={false}
      />
    </>
  )
}
