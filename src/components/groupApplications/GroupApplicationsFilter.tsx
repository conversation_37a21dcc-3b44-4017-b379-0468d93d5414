import React from "react";
import {Flex, Select, SelectProps, Space, Switch} from "antd";
import Search from "antd/es/input/Search";

interface Props {
    searchText: string;
    setSearchText: React.Dispatch<React.SetStateAction<string>>;
    corpUnits: string[];
    setCorpUnits: (value: string[]) => void;
    atLeastOneBusinessArea: boolean;
    setAtLeastOneBusinessArea: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function GroupApplicationsFilter({
                                                    searchText,
                                                    setSearchText,
                                                    corpUnits,
                                                    setCorpUnits,
                                                    atLeastOneBusinessArea,
                                                    setAtLeastOneBusinessArea
                                                }: Props) {
    const corpUnitOptions: SelectProps["options"] = [
        { label: "AT", value: "AT" },
        { label: "CF", value: "CF" },
        { label: "CH", value: "CH" },
        { label: "DE", value: "DE" },
        { label: "ES", value: "ES" },
        { label: "FR", value: "FR" },
        { label: "ITA", value: "ITA" },
    ];

    return (
        <Flex justify="space-between" className="filter-container" style={{marginTop:"16px"}}>
            <Space className="filter-space">
                <Search
                    placeholder="Search..."
                    value={searchText}
                    onChange={(e) => {setSearchText(e.target.value)}}
                    style={{width: "200px"}}
                    className="search-input"
                />
                <div className="filter-item">
                    <span>Apps with at least one business area (AT, DE, FR, ITA, ES, CAS): </span>
                    <Switch
                        checked={atLeastOneBusinessArea}
                        onChange={(value) => setAtLeastOneBusinessArea(value)}
                    />
                </div>
                <div className="filter-item">
                    <span>Corp Units:</span>
                    <Select
                        mode="multiple"
                        allowClear
                        style={{width: "150px"}}
                        className="corp-select"
                        placeholder="Please select"
                        value={corpUnits}
                        onChange={(value: string[]) => setCorpUnits(value)}
                        options={corpUnitOptions}
                    />
                </div>
            </Space>
        </Flex>
    );
}