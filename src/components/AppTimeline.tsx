// Import interfaces
import type {
  AppFactSheet,
  AppRelations,
  CustomPhaseNames,
  FactSheet,
  FoldingDrillDown,
  LifecyclePhase,
  RoadmapColors
} from '../models/data'
import type { TimelineDates } from '../models/timeline.models'
import dayjs from 'dayjs'

// Import react
import React, { useContext } from 'react'
// Import contexts
import { appsDrillDownsContext, FoldingDrillDownContext, RoadmapColorsContext } from '../contexts/Context'

import { convertLifecyclePhaseDates, prepareTimelineData } from '../helpers/CreateTimelineData'
import { parseSingleAppLifeCycle } from '../helpers/ParseLifecycle'

// Import Type check
import { startDateIsNumber } from '../helpers/TypeCheck'
import './AppTimeline.css'

interface AppTimelineProps {
  app: AppFactSheet
  timelineDates: TimelineDates
  yearWidth: number
  zoomLevel: number
  showLabels: boolean
  years: number[]
  openModal: (app: AppFactSheet) => void
}

export default function AppTimeline({
  app,
  timelineDates,
  yearWidth,
  zoomLevel,
  showLabels,
  years,
  openModal
}: AppTimelineProps) {
  const drillDownItems: AppRelations = useContext(appsDrillDownsContext)
  const foldingDrillDownContext: FoldingDrillDown = useContext(FoldingDrillDownContext)

  const foldedDrillDownPhases = (app.id in foldingDrillDownContext.foldedDrillDown ? foldingDrillDownContext.foldedDrillDown[app.id] : true)

  const colors: RoadmapColors = useContext(RoadmapColorsContext)

  const phasesDivs = renderPhases(app)

  const drillDownsPhases = (function () {
    if (foldingDrillDownContext.drillDownBy !== '' && !foldedDrillDownPhases) {
      return drillDownItems[app.id][foldingDrillDownContext.drillDownBy].map((fs: FactSheet) => {
        const updatedFs = JSON.parse(JSON.stringify(fs))

        if (fs.type === 'Application') {
          let fsToDisplay = parseSingleAppLifeCycle(JSON.parse(JSON.stringify(updatedFs)))
          fsToDisplay = prepareTimelineData([fsToDisplay as AppFactSheet], timelineDates, yearWidth, 'alphabetical')[0]

          return (
            <div
              className="app-wrapper-drilldown-item"
              key={app.id + fs.id + timelineDates.startDate + timelineDates.endDate + yearWidth + zoomLevel}
              style={{ width: years.length * yearWidth * zoomLevel }}
              onClick={() => {
                // setOpenedApp(fsToDisplay);
                // setIsModalOpen(true);
              }}
            >
              {renderPhases(fsToDisplay)}
            </div>
          )
        }
        else {
          const data = convertLifecyclePhaseDates(JSON.parse(JSON.stringify(fs)), timelineDates, yearWidth)

          return (
            <div
              className="app-wrapper-drilldown-item"
              key={app.id + fs.id + timelineDates.startDate + timelineDates.endDate + yearWidth + zoomLevel}
              style={{ width: years.length * yearWidth * zoomLevel }}
            >
              {renderPhases(data)}
            </div>
          )
        }
      })
    }
  })()

  return (
    <>
      <div
        className="app-wrapper"
        key={app.id + timelineDates.startDate + timelineDates.endDate + yearWidth + zoomLevel}
        style={{ width: years.length * yearWidth * zoomLevel }}
        onClick={() => {
          // setOpenedApp(app);
          // setIsModalOpen(true);
        }}
      >
        {phasesDivs}
      </div>
      {drillDownsPhases}
    </>
  )

  function getPrevPhase(phase: LifecyclePhase, phases: LifecyclePhase[]) {
    const index = phases.indexOf(phase)

    const filtered = [...phases].splice(0, index + 1).filter(p => !p.phase.includes('Milestone'))
    if (filtered.length > 0) {
      return filtered[filtered.length - 1]
    }
    return undefined
  }

  function renderPhases(fs: FactSheet) {
    if (!fs.validLifecycle && fs.type === 'Application') {
      return (
        <div
          id="invalidLifecycle"
          key="invalidLifecycle"
          className="app-line"
          onClick={() => openModal(fs as AppFactSheet)}
          style={{
            marginLeft: '0px',
            width: '100%',
            backgroundColor: 'violet'
          }}
        >
          <span style={{ color: 'white', marginLeft: '16px' }}>Invalid Lifecycle</span>
        </div>
      )
    }

    if (!fs.lifecycle.phases) {
      return
    }

    return fs.lifecycle.phases.map((phase: LifecyclePhase, idx: number, phases: LifecyclePhase[]) => {
      // Phase if the last one for this app
      if (idx == phases.length - 1) {
        const endOfLifePhase = fs.lifecycle.phases.filter(p => p.phase === 'endOfLife')
        let visibilityTimeline = true

        if (endOfLifePhase.length > 0) {
          const eof = endOfLifePhase[0]

          if (eof.phaseDate === phase.phaseDate && phase.phase.includes('SwitchOffMilestone')) {
            visibilityTimeline = false
          }
          // if(phases.in)
        }

        // If there is only one milestone/phase in the entire line, then we need to set margin
        if (idx == 0) {
          if (!startDateIsNumber(phase.startDate)) {
            throw new Error(`Could not calculate margin because phase.startDate is not of type number:${phase.startDate}`)
          }
          if (!startDateIsNumber(phases[idx].startDate)) {
            throw new Error(`Could not calculate margin because phases[idx].startDate is not of type number:${phases[idx].startDate}`)
          }

          const bgColor = visibilityTimeline ? colors.phaseColors[phase.phase].color : ''

          return (
            <div
              id={phase.phase + timelineDates.startDate + timelineDates.endDate}
              key={phase.phase + timelineDates.startDate + timelineDates.endDate + yearWidth + zoomLevel}
              className={`app-line${phase.phase == 'endOfLife' ? ' eol' : ''}`}
              onClick={() => openModal(fs as AppFactSheet)}
              style={{
                marginLeft: `${phase.startDate * zoomLevel}px`,
                width: `calc(100% - ${phases[idx].startDate as number * zoomLevel}px)`,
                backgroundColor: bgColor
              }}
            >
              {(phase.phase == 'endOfLife' ? '-' : '')}
            </div>
          )
        }

        // If there are multiple milestones/phases in one line
        else {
          // SAME LOGIK WIE UNTEN
          let prevPhase: LifecyclePhase | undefined // phases[idx-1];

          if (phase.phase.includes('Milestone')) {
            prevPhase = getPrevPhase(phase, phases)
          }

          if (!startDateIsNumber(phase.startDate)) {
            throw new Error(`Could not calculate margin because phase.startDate is not of type number:${phase.startDate}`)
          }

          const bgColor = visibilityTimeline ? colors.phaseColors[phase.phase.includes('Milestone') ? prevPhase!.phase : phase.phase].color : undefined

          return (

            <div
              id={phase.phase + timelineDates.startDate + timelineDates.endDate}
              key={phase.phase + timelineDates.startDate + timelineDates.endDate + yearWidth}
              onClick={(event) => {
                console.log('Event')
                const elem = event.target as Element
                let clickable = true
                for (let i = 0; i < elem.children.length; i++) {
                  const child = elem.children[i]
                  if (child.className.includes('specialEol')) {
                    clickable = false
                    break
                  }
                }
                if (clickable) {
                  openModal(fs as AppFactSheet)
                }
              }}
              className={`app-line${phase.phase == 'endOfLife' ? ' eol' : ''}`}
              style={{
                width: `calc(100% - ${phase.startDate * zoomLevel}px)`,
                backgroundColor: bgColor
              }}
            >
              {
                /* Check if this phase is a milestone phase */
                (phase.phase as CustomPhaseNames && phase.phase[0] === '_' && phase.startDate !== 0) && (
                  <>
                    <div
                      className={idx != 0 ? 'diamond' : ''}
                      style={{
                        backgroundColor: colors.diamondColors[phase.phase as CustomPhaseNames],
                        marginTop: '38px'
                      }}
                    >
                    </div>
                    <div
                      className={`${idx != 0 ? 'diamond-label' : ''} ${!showLabels ? 'hidden' : ''}`}
                      style={{ position: 'absolute' }}
                    >
                      <label>
                        {phase.label}
                        <br />
                        {dayjs(phase.phaseDate).format('DD.MM.YYYY')}
                      </label>
                    </div>
                  </>
                )
              }
              {!visibilityTimeline && (
                <div className="eol specialEol" style={{ backgroundColor: 'rgb(192, 0, 0)', zIndex: 19 }}></div>
              )}
            </div>
          )
        }
      }

      // Phase is not the last one
      else {
        let prevPhase: LifecyclePhase | undefined // phases[idx-1];

        if (phase.phase.includes('Milestone')) {
          prevPhase = getPrevPhase(phase, phases)
        }

        if (!startDateIsNumber(phase.startDate)) {
          throw new Error(`Could not calculate margin because phase.startDate is not of type number:${phase.startDate}`)
        }
        return (
          <div
            id={phase.phase + timelineDates.startDate + timelineDates}
            key={phase.phase + timelineDates.startDate + timelineDates.endDate + yearWidth}
          >
            <div
              className="app-line"
              onClick={() => openModal(fs as AppFactSheet)}
              style={{
                marginLeft: idx === 0 ? `${phase.startDate * zoomLevel}px` : '',
                width: `${(phases[idx + 1].startDate as number - (phases[idx].startDate as number)) * zoomLevel}px`,
                backgroundColor: colors.phaseColors[prevPhase ? prevPhase!.phase : phase.phase].color
              }}
            >
            </div>

            {
              /* Check if this phase is a milestone phase */
              (phase.phase as CustomPhaseNames && phase.phase[0] === '_' && phase.startDate !== 0) && (
                <>
                  <div
                    className={idx != 0 ? 'diamond' : ''}
                    onClick={() => openModal(fs as AppFactSheet)}
                    style={{ backgroundColor: colors.diamondColors[phase.phase as CustomPhaseNames], cursor: 'pointer' }}
                  >
                  </div>
                  <div
                    className={`${idx != 0 ? 'diamond-label' : ''} ${!showLabels ? 'hidden' : ''}`}
                    onClick={() => openModal(fs as AppFactSheet)}
                    style={{ position: 'absolute' }}
                  >
                    <label>
                      {phase.label}
                      <br />
                      {dayjs(phase.phaseDate).format('DD.MM.YYYY')}
                    </label>
                  </div>
                </>
              )
            }
          </div>
        )
      }
    })
  }
}
