import type { AppFactSheet, FoldedClusters } from '@app/models/data'
import React from 'react'
import ListRow from './ListRow'

import './AppCluster.css'

interface AppClusterProps {
  apps: AppFactSheet[]
  tagColor: string
  clusterName?: string
  folded: boolean
  toggleFoldingClusterWrapped: () => void
  toggleFoldingDrillDown: (clusterId: string) => void
  clusterBy: string
  drillDown?: string
  foldedDrillDown?: FoldedClusters | null
  openModal: (app: AppFactSheet) => void
  setSidePaneApp: React.Dispatch<React.SetStateAction<string>>
  sidePaneApp: string
}

export default function AppCluster({
  apps,
  clusterName,
  tagColor,
  folded,
  toggleFoldingClusterWrapped,
  toggleFoldingDrillDown,
  clusterBy,
  drillDown,
  foldedDrillDown,
  openModal,
  setSidePaneApp,
  sidePaneApp
}: AppClusterProps) {
  return (
    <>

      <div className="cluster-row-wrap" onClick={() => toggleFoldingClusterWrapped()}>

        {/* TODO: dont display empty clusters */}

        <div className="cluster-row-name">
          <i className={`arrow rotate-arrow-right ${folded ? 'closed' : ''}`}></i>
          <p className="cluster-text" style={{ backgroundColor: tagColor }}>
            {clusterName}
          </p>
        </div>
      </div>

      {!folded && apps.map((app: any) => {
        return (
          <ListRow
            app={app}
            key={app.id + clusterBy + clusterName}
            type="app"
            drillDown={drillDown || null}
            foldedDrillDown={foldedDrillDown ? foldedDrillDown[app.id] : null}
            toggleFoldingDrillDown={toggleFoldingDrillDown}
            openModal={openModal}
            setSidePaneApp={setSidePaneApp}
            sidePaneApp={sidePaneApp}
          />
        )
      })}

    </>
  )
}
