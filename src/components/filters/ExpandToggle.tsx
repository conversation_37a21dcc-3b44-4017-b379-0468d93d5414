import { Switch } from 'antd'
import React from 'react'

interface Props {
  expandAll: boolean
  setExpandAll: React.Dispatch<React.SetStateAction<boolean>>
}

const ExpandToggle = ({ expandAll, setExpandAll }: Props) => {
  return (
    <Switch
      style={{ marginLeft: '8px' }}
      value={expandAll}
      onChange={() => setExpandAll(!expandAll)}
      checkedChildren="Unexpand all"
      unCheckedChildren="Expand all"
      defaultChecked
    />
  )
}

export default ExpandToggle
