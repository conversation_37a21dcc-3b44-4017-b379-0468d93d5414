import { CORP_UNITS, CORP_UNITS_SMALL } from '@config/settings'
import { Segmented } from 'antd'
import React from 'react'

interface Props {
  corpUnit: string
  setCorpUnit: React.Dispatch<React.SetStateAction<string>>
  small: boolean
}

const CorpUnitFilter = ({ corpUnit, setCorpUnit, small }: Props) => {
  return (
    <Segmented onChange={(value: string) => setCorpUnit(value)} value={corpUnit} options={small ? CORP_UNITS_SMALL : CORP_UNITS} />
  )
}

export default CorpUnitFilter
