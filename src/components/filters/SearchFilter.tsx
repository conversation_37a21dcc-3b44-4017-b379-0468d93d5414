import Search from "antd/es/input/Search";
import React from "react";


interface Props {
    search: string;
    setSearch: React.Dispatch<React.SetStateAction<string>>;
}

const SearchFilter = ({search, setSearch}: Props) => {


    return (
        <Search
            placeholder="Search..."
            onChange={(e) => setSearch(e.currentTarget.value)}
            style={{width: "100%"}}
            allowClear
        />
    );

}

export default SearchFilter;