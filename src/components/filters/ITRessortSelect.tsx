import type { ITRessort, RessortApplication } from '@app/model'
import { InfoCircleOutlined } from '@ant-design/icons'
import { getITResorts, statColor } from '@app/helpers'
import { ressortOverview } from '@config/ressortOverview'
import { applyITRessortFilter } from '@utils/filters'
import { Badge, Select, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'

const BADGE_WIDTHS = {
  apps: '60px',
  completed: '80px',
  missing: '100px'
}
const SELECTED_ITEM_PADDING = '24px'

const formatRessortItem = (ressort: ITRessort, isSelected: boolean = false) => (
  <div style={{
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingRight: isSelected ? SELECTED_ITEM_PADDING : '0'
  }}
  >
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span>{ressort.description}</span>
    </div>

    <div style={{ display: 'flex', gap: '4px' }}>
      <div style={{ width: BADGE_WIDTHS.apps, textAlign: 'right' }}>
        <Badge
          count={`${ressort.appCount} Apps`}
          style={{ backgroundColor: 'blue' }}
          overflowCount={999}
        />
      </div>
      <div style={{ width: BADGE_WIDTHS.completed, textAlign: 'right' }}>
        <Badge
          count={`${ressort.completed}%`}
          style={{ backgroundColor: statColor(ressort.completed) }}
        />
      </div>
      <div style={{ width: BADGE_WIDTHS.missing, textAlign: 'right' }}>
        <Badge
          count={`${ressort.missing} Fields`}
          style={{ backgroundColor: statColor(ressort.completed) }}
          overflowCount={999}
        />
      </div>
    </div>
  </div>
)

interface Props {
  onChange?: (value: string) => void
  applications: RessortApplication[]
}

const ITRessortSelect = ({ onChange, applications }: Props) => {
  const [itRessorts, setItRessorts] = useState<ITRessort[]>([])
  const [selectedRessort, setSelectedRessort] = useState<string | null>(null)

  useEffect(() => {
    const loadRessorts = getITResorts()
    loadRessorts.then((data) => {
      let parsed: ITRessort[] = data.map((resort) => {
        // Verwende den gleichen Filter wie in applyITRessortFilter
        const filteredApps = applyITRessortFilter(applications, resort.id) as RessortApplication[]

        // Berechne Statistiken für das Ressort
        const stats = ressortOverview.calculateStats(filteredApps)

        const appStat = stats.find(s => s.id === 'applications')?.value

        return {
          ...resort,
          appCount: appStat ? Number.parseInt(appStat) : 0,
          completed: Number((stats.find(s => s.id === 'completed')?.value) || -1),
          missing: Number((stats.find(s => s.id === 'missing')?.value) || -1)
        }
      })

      parsed = parsed.sort((a, b) => b.completed - a.completed)
      setItRessorts([...parsed])
    })
  }, [applications])

  // Dropdown-Optionen formatieren
  const options = itRessorts.map(ressort => ({
    value: ressort.id,
    label: formatRessortItem(ressort)
  }))

  const handleChange = (value: string) => {
    setSelectedRessort(value)
    if (onChange) {
      onChange(value)
    }
  }

  return (
    <div style={{ width: '100%', maxWidth: '550px', minWidth: '550px' }}>
      <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between' }}>
        <span style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.85)' }}>
          IT Ressort
          <Tooltip title="Wählen Sie ein IT-Ressort aus">
            <InfoCircleOutlined style={{ marginLeft: '4px', color: '#bfbfbf' }} />
          </Tooltip>
        </span>

        <div style={{ display: 'flex', color: 'rgba(0, 0, 0, 0.65)', fontSize: '12px' }}>
          <div style={{ width: BADGE_WIDTHS.apps, textAlign: 'left' }}>Total</div>
          <div style={{ width: BADGE_WIDTHS.completed, textAlign: 'left' }}>Completed</div>
          <div style={{ width: BADGE_WIDTHS.missing, textAlign: 'left' }}>Missing Values</div>
        </div>
      </div>

      <Select
        placeholder="IT-Ressort auswählen"
        style={{ width: '100%' }}
        options={options}
        onChange={handleChange}
        value={selectedRessort}
        labelInValue={false}
        allowClear
        optionLabelProp="children"
        dropdownStyle={{ minWidth: '350px' }}
        labelRender={(item) => {
          const ressort = itRessorts.find(r => r.id === item.value)
          return ressort ? formatRessortItem(ressort, true) : item.label
        }}
      />
    </div>
  )
}

export default ITRessortSelect
