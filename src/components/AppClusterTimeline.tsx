// import interfaces
import type { AppCluster, AppFactSheet } from '../models/data'
import type { TimelineDates } from '../models/timeline.models'

import React from 'react'
import AppTimeline from './AppTimeline'

interface AppClusterTimelineProps {
  cluster: AppCluster
  timelineDates: TimelineDates
  yearWidth: number
  zoomLevel: number
  showLabels: boolean
  years: number[]
  folded: boolean
  openModal: (app: AppFactSheet) => void
}

export default function AppClusterTimeline({ cluster, timelineDates, yearWidth, zoomLevel, showLabels, years, folded, openModal }: AppClusterTimelineProps): JSX.Element {
  return (
    <>
      <div style={{ backgroundColor: 'rgb(240, 242, 245)', zIndex: -1 }} className="app-wrapper">
      </div>
      {!folded
        ? cluster.apps.map((app) => {
            return (
              <AppTimeline
                app={app}
                key={app.id}
                timelineDates={timelineDates}
                yearWidth={yearWidth}
                zoomLevel={zoomLevel}
                showLabels={showLabels}
                years={years}
                openModal={openModal}
              />
            )
          })
        : <></>}
    </>
  )
}
