import { ExportOutlined } from '@ant-design/icons'
import { Button, Space } from 'antd'
import React from 'react'

interface Props {
  exportData: (xlsx: boolean) => void
}

export default function ExportMenu({ exportData }: Props) {
  return (
    <Space>
      <Button
        icon={<ExportOutlined />}
        size="small"
        onClick={() => exportData(true)}
      >
        export XLSX
      </Button>
      <Button
        icon={<ExportOutlined />}
        size="small"
        onClick={() => exportData(false)}
      >
        export CSV
      </Button>
    </Space>
  )
}
