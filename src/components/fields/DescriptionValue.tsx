import {Tag, Tooltip} from "antd";
import React from "react";


interface Props {
    value: string | undefined | null;
}

export default function DescriptionValue({value}: Props) {

    if (value && value.length >= 50) {
        return (<Tooltip placement="topLeft" title={value}>
            {value}
        </Tooltip>)
    } else {
        return <Tooltip placement="topLeft" title={value}>
            <Tag style={{width: "100%", textAlign: "center", overflow:"hidden"}} color="red">{(value ||"").length > 0 ? value : "-"}</Tag>
        </Tooltip>
    }


}