import { InfoCircleOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import React from 'react'

interface Props {
  text: string
}

export default function ValidationInfo({ text }: Props) {
  const content = (
    <div style={{ maxWidth: '150px' }}>
      <p>{text}</p>
    </div>
  )

  return (
    <Popover style={{ maxWidth: '150px' }} content={content} title="Validation">
      <InfoCircleOutlined />
    </Popover>
  )
}
