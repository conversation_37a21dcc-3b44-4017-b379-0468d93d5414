import {CaretUpOutlined} from "@ant-design/icons";
import {Button, Flex, Tooltip} from "antd";
import React from "react";
import {statColor} from "@app/helpers";

interface Props {
    appId: string;
    score: number;
}

export default function WizardLink({appId, score}: Props) {

    const url = `/reports/eam_wizard/new?usecase=checkApplication&id=${appId}`;
    const color = statColor(score);

    const getText = (color: "green" | "orange" | "red") => {

        if(color === "green"){
            return  "The most important fields are set, but it is still recommended to check that they are up to date/complete. Click on the button to simply complete/update the application."
        } else if(color === "orange"){
            return  "The application should be checked and updated to achieve high data quality. Click on the button to simply complete/update the application."
        } else if(color === "red"){
            return  "The application is missing some important information. Please complete these in order to achieve an appropriate data quality. Click on the button to simply complete/update the application."
        } else {
            return "";
        }


    }


    return (
        <Flex justify={"center"}>
            <Tooltip title={getText(color)}>
                <Button size="small" style={{backgroundColor: color}}  type="primary"  onClick={() => lx.openLink(url)}
                        icon={<CaretUpOutlined/>}/>
            </Tooltip>
        </Flex>
    );

}