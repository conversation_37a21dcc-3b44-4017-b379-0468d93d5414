import type { BaseApplication } from '@app/model'
import { Popover, Progress, Table, Tag } from 'antd'
import React from 'react'

interface Props {
  application: BaseApplication
}

export default function LifeCycleView({ application }: Props) {
  const phaseIn = application.lifecycle?.phases.find(p => p.phase === 'phaseIn')
  const active = application.lifecycle?.phases.find(p => p.phase === 'active')
  const phaseOut = application.lifecycle?.phases.find(p => p.phase === 'phaseOut')

  const isOkay = !!phaseIn || !!active || !!phaseOut

  const columns = [
    {
      title: 'Phase',
      dataIndex: 'phase',
      key: 'phase',
      render: (text: string) => lx.translateFieldValue('Application', 'lifecycle', text)
    },
    {
      title: 'Date',
      dataIndex: 'startDate',
      key: 'startDate'
    }
  ]

  const data = [
    {
      phase: 'Phase In',
      startDate: phaseIn ? `${phaseIn.startDate}` : <Tag style={{ width: '100%', textAlign: 'center' }} color="red">-</Tag>
    },
    {
      phase: 'Go-Live',
      startDate: active ? `${active.startDate}` : <Tag style={{ width: '100%', textAlign: 'center' }} color="red">-</Tag>
    },
    {
      phase: 'Phase Out',
      startDate: phaseOut ? `${phaseOut.startDate}` : <Tag style={{ width: '100%', textAlign: 'center' }} color="red">-</Tag>
    }
  ]

  return (
    <Popover
      content={(
        <Table
          pagination={false}
          size="small"
          dataSource={data}
          columns={columns}
        />
      )}
      title="Lifecycle"
    >
      <Progress
        status={isOkay ? 'success' : 'exception'}
        percent={100}
        steps={3}
        strokeColor={[
          phaseIn ? 'green' : 'orange',
          active ? 'green' : 'orange',
          phaseOut ? 'green' : 'orange'
        ]}
        showInfo
      />
    </Popover>
  )
}
