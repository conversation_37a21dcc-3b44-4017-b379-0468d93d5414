import type { DataQualityApplication } from '@app/model'
import { CheckCircleTwoTone, CloseCircleTwoTone } from '@ant-design/icons'
import { Popover } from 'antd'
import React from 'react'

interface Props {
  app: DataQualityApplication
}

export default function L3Field({ app }: Props) {
  if (app.hasL3) {
    const content = (
      <div>
        <p>These applications should be migrated to level 2 applications:</p>
        <ul>
          {app.l3Apps?.map(l3 => (<li key={l3.id}><a onClick={() => lx.openLink(`/factsheet/Application/${l3.id}`)}>{l3.name}</a></li>)) }
        </ul>
      </div>
    )

    return (<Popover content={content} title="Level 3 Applications"><CloseCircleTwoTone twoToneColor="#eb2f96" /></Popover>)
  }
  else {
    return (<CheckCircleTwoTone twoToneColor="#52c41a" />)
  }
}
