import ValueTag from '@components/basic/ValueTag'
import { Space, Tag, Tooltip } from 'antd'
import React from 'react'

interface Props {
  value: string | undefined | null | string[]
  limit?: number
}

export default function FieldValue({ value, limit }: Props) {
  if (!value) {
    return <Tag style={{ width: '100%', textAlign: 'center' }} color="red">-</Tag>
  }

  if (Array.isArray(value)) {
    return (
      <Space direction="vertical">
        {value.map((item, index) => (
          <ValueTag limit={limit} text={item} key={index} />
        ))}
      </Space>
    )
  }

  return (
    <Tooltip placement="topLeft" title={value}>
      {value}
    </Tooltip>
  )
}
