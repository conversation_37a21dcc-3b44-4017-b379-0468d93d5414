import {Flex, Tag, Tooltip} from "antd";
import React from "react";
import {BaseApplication} from "@app/model";
import {statColor} from "@app/helpers";


interface Props {
    app: BaseApplication;
}

export default function CompleteScoreView({app}:Props){

    if(app.relevantForCalculations) {
        return (<Flex align={"end"}><Tag color={statColor(app.completed)}>{app.completed}%</Tag></Flex>)
    } else {
        return (<Flex align={"end"}>
            <Tooltip title={"This application is not applicable for the calculation due to a different corporate unit. However, please review the associated child applications."}>
            <Tag
            color={statColor(app.completed)}>{!app.relevantForCalculations ? "*" : undefined}{app.completed}%</Tag></Tooltip></Flex>)
    }

}