import {Col, Row, Table, Tag, Typography} from "antd";
import React from "react";
import {ITComponent} from "../model";
import {useVT} from "virtualizedtableforantd4";
import {ColumnsType} from "antd/es/table";

interface Props {
    data: ITComponent[];
}

export default function DataTable({data}:Props){

    const [ vtComponents ] = useVT(() => ({scroll: {y: "calc(100vh - 230px)"}}), []);

    const columns: ColumnsType<ITComponent> = [
        {
            title: "Name",
            dataIndex: "displayName",
            key: "displayName",
            sorter: (a, b) => a.displayName.localeCompare(b.displayName),
        },
        {
            title: "Primary Use",
            dataIndex: "primaryUse",
            render: (text:string, record:ITComponent) => (record.primaryUse ? <span>{record.primaryUse.map((p) => <Tag key={p}>{p}</Tag>)}</span> : undefined),
            filters: [...new Set([...data.map(itComp => itComp.primaryUse).filter(primaryUse => primaryUse !== undefined)].flat())].map(primaryUse => ({text:primaryUse, value:primaryUse})),
            onFilter: (value, record) => record.primaryUse ? record.primaryUse.includes(value.toString()) : false
        },
        {
            title: "Potential Issues",
            dataIndex: "potentialIssues",
            sorter: (a, b) => a.potentialIssues.localeCompare(b.potentialIssues),
            render: (text) => text.length > 0 ? `${text.substring(0, 50)}${text.length > 50 ? "..." : ""}` : ""
        },
        {
            title: "Legal Compliance and Risk Assesment",
            dataIndex: "legalComplianceAndRisk",
            filters: [...new Set([...data.map(itComp => itComp.legalComplianceAndRisk).filter(legal => legal !== "")])].map(legal => ({text:legal, value:legal})),
            onFilter: (value, record) => record.legalComplianceAndRisk == value,
            sorter: (a, b) => a.legalComplianceAndRisk.localeCompare(b.legalComplianceAndRisk),
        },
        {
            title: "Comments",
            dataIndex: "comments",
            sorter: (a, b) => a.comments.localeCompare(b.comments),
            render: (text) => text.length > 0 ? `${text.substring(0, 50)}${text.length > 50 ? "..." : ""}` : ""
        },
        {
            title: "Last checked by CCC",
            dataIndex: "lastCheckedByCCC",
            sorter: (a, b) => a.lastCheckedByCCC.localeCompare(b.lastCheckedByCCC),
            width:180
        },
        {
            title: "Ml OPt Out",
            dataIndex: "mlOPtOut",
            filters: [{text: "YES", value: "YES"},{text: "NO", value: "NO"}],
            onFilter: (value, record) => record.mlOPtOut == value,
            sorter: (a, b) =>  String(a.mlOPtOut).localeCompare(String(b.mlOPtOut)),
            width:140
        },
    ];

    const { Paragraph } = Typography;

    return (
        <Table rowKey="id" size="small"
               dataSource={data} columns={columns} components={vtComponents}
               scroll={{y: "calc(100vh - 230px)"}} pagination={false}
               expandable={{
                   expandedRowRender: (record) => (
                       <>
                           <Row>
                               <Col span={24}>
                                   <Paragraph strong={true}>Potential Issues:</Paragraph>
                               </Col>
                               <Col span={24}>
                                   <Paragraph>{record.potentialIssues}</Paragraph>
                               </Col>
                           </Row>
                           <Row>
                               <Col span={24}>
                                   <Paragraph strong={true}>Comments: </Paragraph>
                               </Col>
                               <Col span={24}>
                                   <Paragraph>{record.comments}</Paragraph>
                               </Col>
                           </Row>
                       </>
                   )
               }}
        />
    );
}