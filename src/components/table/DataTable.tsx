import {Table, TablePaginationConfig} from "antd";
import React from "react";
import {Application} from "@app/model";
import {ColumnsType} from "antd/es/table";
import {FilterValue, SorterResult, TableCurrentDataSource} from "antd/es/table/interface";
import useScreenSize from "@utils/useScreenSize";

interface Props {
    data: Application[];
    columns: ColumnsType<Application>;
    expandedKeys: string[];
    setExpandedKeys: React.Dispatch<React.SetStateAction<string[]>>;
    handleChange: (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<Application> | SorterResult<Application>[],
                   extra: TableCurrentDataSource<Application>) => void;
    setExportData: React.Dispatch<React.SetStateAction<Application[]>>;
}

export default function DataTable({data, columns, expandedKeys,setExpandedKeys, handleChange, setExportData}: Props) {


    const size = useScreenSize();


    return (<Table dataSource={data}
                   columns={columns}
                     virtual
                   rowKey="id"
                   pagination={false}
                   size="small"
                   scroll={{y: size.height - 200 > 200 ? size.height - 250 : 250, x: 'max-content'}}
                   expandable={
                       {
                           defaultExpandAllRows: true, childrenColumnName: "children", showExpandColumn: true,
                           expandedRowKeys: [...expandedKeys],
                           onExpand: (expanded, record) => {
                               if(expanded) {
                                   setExpandedKeys(prevState => [...prevState, record.id]);
                               } else {
                                   setExpandedKeys(prevState => prevState.filter(key => key !== record.id));
                               }
                           }
                       }}
                   rowClassName={(record) => {
                       return !record.relevantForCalculations ? 'gray-out' : '';
                   }}

                   onChange={(pagination, filters, sorter, extra) => {
                       //console.log("APPLY LOCAL DATA")
                       const noFilters = Object.values(filters).every(filter => !filter || filter.length === 0);
                       const noSorter = !Array.isArray(sorter) ? !sorter.order : sorter.length === 0;

                       if (noFilters && noSorter) {
                           setExportData([]);
                       } else {
                           setExportData(extra.currentDataSource);
                       }
                       console.log(sorter)
                       handleChange(pagination, filters, sorter, extra);
                   }}
    />);

}