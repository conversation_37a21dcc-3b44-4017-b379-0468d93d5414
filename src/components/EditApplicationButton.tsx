import {EditOutlined} from "@ant-design/icons";
import React from "react";
import {Tooltip} from "antd";


interface Props {
    onClick: (e: React.MouseEvent) => void;
}

export default function EditApplicationButton({onClick}: Props) {

    return (
            <Tooltip title="Edit Milestones" placement="right">
            <span className='row-icon' style={{marginRight: "2px", marginLeft:"4px", backgroundColor: "orange"}}>
                <EditOutlined onClick={(event) => onClick(event)}/>
            </span>
            </Tooltip>
    );

}