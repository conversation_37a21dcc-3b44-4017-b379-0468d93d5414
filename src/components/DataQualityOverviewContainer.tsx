import type { Application, Dashboard, Filters, RessortApplication, Sorts, Statistic } from '@app/model'
import StatsContainer from '@components/basic/StatsContainer'
import ExportMenu from '@components/fields/ExportMenu'
import CorpUnitFilter from '@components/filters/CorpUnitFilter'

import ExpandToggle from '@components/filters/ExpandToggle'
import ITRessortSelect from '@components/filters/ITRessortSelect'
import SearchFilter from '@components/filters/SearchFilter'
import DashboardSelector from '@components/navigation/DashboardSelector'
import DataTable from '@components/table/DataTable'
import { DASHBOARDS } from '@config/settings'
import {
  applyCorpUnitFilter,
  applyCorpUnitFilterToITRessortResults,
  applyITRessortFilter,
  applyTextSearchFilter
} from '@utils/filters'
import { Col, Flex, Row, Skeleton, Space } from 'antd'
import Title from 'antd/es/typography/Title'
import React, { useEffect, useState } from 'react'

interface Props {
  rawData: any[]
  loading: boolean
  defaultDashboard: Dashboard | null
}

export default function DataQualityOverviewContainer({ rawData, loading, defaultDashboard }: Props) {
  const [parseLoading, setParseLoading] = useState<boolean>(true)

  const [selectedDashboard, setSelectedDashboard] = useState<Dashboard>(defaultDashboard || DASHBOARDS[0])
  const [stats, setStats] = useState<Statistic[]>([])
  const [applications, setApplications] = useState<Application[]>([])
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [appsInTable, setAppsInTable] = useState<Application[]>([])

  // Filters
  const [corpUnit, setCorpUnit] = useState<string>('CH')
  const [searchText, setSearchText] = useState<string>('')
  const [itResort, setItResort] = useState<string>('')

  // Expand
  const [expand, setExpand] = useState<boolean>(true)
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])

  const [filteredInfo, setFilteredInfo] = useState<Filters>({})
  const [sortedInfo, setSortedInfo] = useState<Sorts>({
    column: {
      title: 'C.',
      width: 90,
      dataIndex: 'completed',
      key: 'completed',
      filters: [
        {
          text: '33%',
          value: 33
        },
        {
          text: '66%',
          value: 66
        },
        {
          text: '0%',
          value: 0
        },
        {
          text: '100%',
          value: 100
        }
      ],
      filteredValue: null
    },
    order: 'ascend',
    field: 'completed',
    columnKey: 'completed'
  })

  useEffect(() => {
    if (expand) {
      const allKeys: string[] = [...applications.filter(app => app.children && app.children.length > 0).map(app => (app.id))]
      setExpandedKeys(allKeys)
    }
    else { setExpandedKeys([]) }
  }, [expand, applications])

  useEffect(() => {
    if (loading) { setParseLoading(true) }
  }, [loading])

  useEffect(() => {
    if (rawData.length > 0) {
      const parsed = selectedDashboard.parse(rawData)

      setApplications(parsed)
      setExpandedKeys([])
      setParseLoading(false)
    }
  }, [rawData, selectedDashboard])

  useEffect(() => {
    if (applications && applications.length > 0) {
      let filtered = [...applications]

      // Text-Suche anwenden
      if (selectedDashboard.filters.includes('textSearch')) {
        filtered = applyTextSearchFilter(filtered, searchText)
      }

      // Corporate Units Filter anwenden
      if ((selectedDashboard.filters.includes('corpUnits') || selectedDashboard.filters.includes('corpUnitsSmall'))) {
        filtered = applyCorpUnitFilter(filtered, corpUnit)
      }

      // IT-Ressort Filter anwenden
      if (selectedDashboard.filters.includes('itRessorts') && itResort) {
        filtered = applyITRessortFilter(filtered, itResort)

        // Zusätzliches Filtern nach corpUnit, wenn ein Wert gesetzt ist
        if (corpUnit !== 'All' && corpUnit !== '') {
          filtered = applyCorpUnitFilterToITRessortResults(filtered, corpUnit)
        }
      }

      // Statistiken berechnen und Zustand aktualisieren
      const calculateStats = selectedDashboard.calculateStats(filtered)
      setStats(calculateStats)
      setFilteredApplications(filtered)
    }
  }, [applications, searchText, corpUnit, itResort, selectedDashboard])

  const highlightText = (text: string, search: string) => {
    const regex = new RegExp(`(${search})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }

  const updateDashboard = (dashboard: Dashboard) => {
    setSelectedDashboard(dashboard)

    if (!window.location.host.includes('localhost')) {
      const url = new URL(document.referrer)
      url.searchParams.set('dashboard', dashboard.id)
      lx.openRouterLink(url.toString())
    }
  }

  useEffect(() => {
    if (defaultDashboard) { setSelectedDashboard(defaultDashboard) }
  }, [defaultDashboard])

  return (
    <Row gutter={0}>
      <Col span={24}>

        <Flex justify="space-between" gap={48}>
          <Space align="baseline">
            <Title style={{ minWidth: '280px' }} level={3}>{selectedDashboard.name}</Title>
            <DashboardSelector
              selectedDashboard={selectedDashboard}
              setSelectedDashboard={updateDashboard}
            />
          </Space>

          <div style={{ marginBottom: '0px' }}>
            <StatsContainer stats={stats} />
          </div>
        </Flex>
      </Col>
      <Col md={18} sm={24}>
        <Flex justify="flex-start" gap={16} align="flex-end" style={{ paddingBottom: '12px' }}>
          <div>
            <Space wrap>
              {selectedDashboard.filters.includes('textSearch') && (
                <SearchFilter search={searchText} setSearch={setSearchText} />
              )}
              {selectedDashboard.filters.includes('unexpandAll') && (
                <ExpandToggle expandAll={expand} setExpandAll={setExpand} />
              )}
            </Space>
          </div>
          <Flex align="flex-end">
            <Space align="end" wrap>
              {(selectedDashboard.filters.includes('corpUnits') || selectedDashboard.filters.includes('corpUnitsSmall')) && (
                <CorpUnitFilter corpUnit={corpUnit} small={selectedDashboard.filters.includes('corpUnitsSmall')} setCorpUnit={setCorpUnit} />
              )}
              {selectedDashboard.filters.includes('itRessorts') && (
                <ITRessortSelect
                  onChange={value => setItResort(value)}
                  applications={applications as RessortApplication[]}
                />
              )}
            </Space>
          </Flex>

        </Flex>
      </Col>
      <Col md={6} sm={24}>
        <Flex justify="flex-end">
          <ExportMenu exportData={xlsx =>
            selectedDashboard.export(xlsx, appsInTable.length === 0 ? filteredApplications : appsInTable)}
          />
        </Flex>
      </Col>
      <Col span={24}>
        {(loading || parseLoading)
          ? <Skeleton active />
          : (
              <DataTable
                data={filteredApplications}
                columns={selectedDashboard.getColumns(applications, sortedInfo, filteredInfo, searchText, highlightText)}
                expandedKeys={expandedKeys}
                setExpandedKeys={setExpandedKeys}
                handleChange={(_, filters, sorter, extra) => {
                  setFilteredInfo(filters)
                  setSortedInfo(sorter as Sorts)
                }}
                setExportData={setAppsInTable}
              />
            )}
      </Col>

    </Row>
  )
}
