import { Select, Skeleton } from 'antd'
import React, { useEffect, useState } from 'react'

interface User {
  value: string
  label: string
}

export default function UserSelectLeanIX({ ...props }) {
  const [options, setOptions] = useState<User[]>([])
  const [loading, setLoading] = useState(!!props.value)

  const parseUserData = (json: any) => {
    const users: User[] = []

    json.data.filter((r: any) => r.status === 'ACTIVE' || r.status === 'INVITED').map((record: any) => {
      const user: User = {
        value: record.user.id,
        label: `${record.user.firstName || ''} ${record.user.lastName || '-'} (${record.user.email})`
      }
      users.push(user)
    })
    return users
  }

  const searchForUsers = async (searchText: string) => {
    const res = await lx.executeParentOriginXHR('GET', `/services/mtm/v1/workspaces/a4a5f283-224d-432e-b8cc-941d93c7c06d/permissions?size=100&sort=user.email-asc&q=${searchText}`)
    const data = JSON.parse(res.body)
    setOptions(parseUserData(data))
  }

  const getUserById = async (userId: string) => {
    const res = await lx.executeParentOriginXHR('GET', `/services/mtm/v1/workspaces/a4a5f283-224d-432e-b8cc-941d93c7c06d/users/${userId}`)
    const data = JSON.parse(res.body).data
    setOptions((prevState) => {
      const newState = [...prevState]
      if (newState.filter(user => user.value === data.id).length === 0) {
        newState.push({
          label: `${data.firstName || ''} ${data.lastName || '-'} (${data.email})`,
          value: data.id
        })
      }
      return newState
    })
  }

  useEffect(() => {
    searchForUsers('').then(() => getUserById(props.value).then(() => setLoading(false)))
  }, [])

  return (
    <>
      {loading
        ? <Skeleton.Input block active />
        : (
            <Select
              {...props}
              showSearch

              allowClear
              placeholder="Search to Select"
              onSearch={(value: string) => searchForUsers(value)}
              optionFilterProp="children"
              filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
              options={options}
            />
          )}
    </>

  )
}
