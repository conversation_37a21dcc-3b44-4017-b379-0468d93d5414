import type { WizardFormModel } from '../../model'
import { <PERSON><PERSON>, <PERSON>, Space, theme, Typography } from 'antd'
import React from 'react'
import { getContentStyle } from '../../constants'

interface Props {
  isMember: boolean
  setSelectedWizard: (form: WizardFormModel | undefined) => void
  showSuccessMessage: boolean
  setShowSuccessMessage: React.Dispatch<React.SetStateAction<boolean>>
  wizards: WizardFormModel[]
}

export default function FactsheetTypeChooser({
  setSelectedWizard,
  isMember,
  showSuccessMessage,
  setShowSuccessMessage,
  wizards
}: Props) {
  const { token } = theme.useToken()
  const { Title } = Typography

  const choose = (mode: string) => {
    setSelectedWizard(wizards.filter(w => w.id == mode)[0])
    setShowSuccessMessage(false)
  }

  return (
    <div style={getContentStyle(token)}>

      {showSuccessMessage && (
        <Space>
          <Alert
            style={{ maxWidth: '700px' }}
            message="Data submitted"
            description="Thank you for entering the data. We will review it within the next few days
         and get back to you if we have any questions. Below you can enter the next factsheet if needed"
            type="success"
            closable
            showIcon
            onClose={() => setShowSuccessMessage(false)}
          />
        </Space>
      )}

      <Title level={5}>What would you like to do?</Title>
      <div style={{ display: 'flex', justifyContent: 'center', gap: '20px', margin: '40px', flexWrap: 'wrap', flexDirection: 'row' }}>
        {/*isMember
          ? (*/
              <>
                {wizards.filter(w => !w.onlyAdmin && !w.id.includes('information-flow')).map(wizard => (
                  <Card
                    key={wizard.id}
                    className="wizard-select-card"
                    style={{ height: '100px' }}
                    onClick={() => choose(wizard.id)}
                  >
                    <Title level={4}>{wizard.name}</Title>
                  </Card>
                ))}
                <Card key="informationflow" className="wizard-select-card" style={{ cursor: 'unset' }}>
                  <Title level={4}>Information Flow</Title>
                  <ul className="information-flow-ul">
                    <li><a onClick={() => choose('information-flow/mft')}>Add MFT</a></li>
                    <li><a onClick={() => choose('information-flow/api')}>Add API / Service</a></li>
                    <li>
                      <a onClick={() => choose('information-flow/kafka-interface')}>
                        Add Kafka Topic -
                        Interface
                      </a>
                    </li>
                    <li>
                      <a onClick={() => choose('information-flow/kafka-consumer')}>
                        Add Kafka Topic -
                        Consumer
                      </a>
                    </li>
                  </ul>
                </Card>
              </>
          /*  )
          : (
              <p>
                You don't have the needed permissions to add factsheets in LeanIX.
                <br />
                {' '}
                To be able to create a
                desktop software
                or application here, you need the "LeanIX Power User Accounts" (GI2576) role.
                <br />
                You can order it in the
                {' '}
                <a
                  onClick={() => lx.openLink('https://rbac.helvetia.com/', '_blank')}
                >
                  RBAC.
                </a>
              </p>
            )*/}
      </div>
    </div>
  )
}
