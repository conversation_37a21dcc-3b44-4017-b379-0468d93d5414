import type { InputRef } from 'antd'
import type { Value } from '../../model'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Divider, Input, Select, Space, Tag, Typography } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { isUUID } from '../../helpers'

const { Text } = Typography

interface Props {
  providers?: Value[]
  value?: string | string[]
  onChange?: (value: string | string[]) => void
  options?: { value: string, label: string }[] | undefined
  mode?: 'multiple' | undefined
  newCustomProviders: Value[]
  setNewCustomProviders: React.Dispatch<React.SetStateAction<Value[]>>
}

export default function ProviderSelect({
  providers = [],
  value = [],
  onChange,
  options,
  mode,
  newCustomProviders,
  setNewCustomProviders,
  ...props
}: Props) {
  const [items, setItems] = useState<Value[]>([])
  const [name, setName] = useState('')
  const [error, setError] = useState<string | null>(null)
  const inputRef = useRef<InputRef>(null)

  const normalizedValue = Array.isArray(value) ? value : value ? [value] : []

  useEffect(() => {
    // Combine and sort providers with newCustomProviders always at the top
    setItems([...newCustomProviders, ...providers])
  }, [providers, newCustomProviders])

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value)
    setError(null)
  }

  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault()

    const trimmedName = name.trim()
    if (!trimmedName) { return }

    // Check for duplicates in both newCustomProviders and providers
    const isDuplicate = [...newCustomProviders, ...providers].some(
      item => item.label.toLowerCase() === trimmedName.toLowerCase()
    )

    if (isDuplicate) {
      setError('This provider already exists.')
      return
    }

    const newItem = { value: trimmedName, label: trimmedName }
    setNewCustomProviders(prev => [newItem, ...prev])

    if (onChange) {
      onChange(mode ? [...normalizedValue, newItem.value] : newItem.value)
    }

    setName('')
    setTimeout(() => {
      inputRef.current?.focus()
    }, 0)
  }

  const tagRender = (props: any) => {
    const { label, value, closable, onClose } = props
    const color = isUUID(value as string) ? 'default' : 'green'
    return (
      <Tag color={color} closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {color === 'green' ? `${label} (new)` : label}
      </Tag>
    )
  }

  const handleChange = (selectedValues: string | string[]) => {
    if (onChange) {
      onChange(selectedValues)
    }
  }

  return (
    <>
      <Select
        showSearch
        {...props}
        mode={mode}
        filterOption={(input: string, option: Value | undefined) => {
          const label = option?.label?.toLowerCase() || ''
          return label.includes(input.toLowerCase())
        }}
        filterSort={(optionA: Value, optionB: Value) => {
          const isNewA = !isUUID(optionA.value as string)
          const isNewB = !isUUID(optionB.value as string)
          if (isNewA !== isNewB) { return isNewA ? -1 : 1 }
          return (optionA?.label?.toLowerCase() || '').localeCompare(optionB?.label?.toLowerCase() || '')
        }}
        placeholder="Search to Select"
        value={value}
        onChange={handleChange}
        dropdownRender={menu => (
          <>
            {menu}
            <Divider style={{ margin: '8px 0' }} />
            <Space style={{ padding: '0 8px 4px' }}>
              <Input
                placeholder="Please enter provider name"
                ref={inputRef}
                value={name}
                style={{ width: 250 }}
                onChange={onNameChange}
                onKeyDown={e => e.stopPropagation()}
              />
              <Button type="text" icon={<PlusOutlined />} onClick={addItem}>
                Add Provider
              </Button>
            </Space>
            {error && (
              <Space style={{ padding: '0 8px 4px' }}>
                <Text type="danger">{error}</Text>
              </Space>
            )}
          </>
        )}
        options={items.map(item => ({
          label: item.label,
          value: item.value
        }))}
        optionLabelProp="label"
        tagRender={mode === 'multiple' ? tagRender : undefined}
        optionRender={option => (
          <span style={{ color: isUUID(option.value as string) ? 'inherit' : 'green' }}>
            {option.label}
          </span>
        )}
      />
    </>
  )
}
