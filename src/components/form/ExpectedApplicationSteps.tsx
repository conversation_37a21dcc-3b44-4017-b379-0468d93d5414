import type { FormInstance } from 'antd'
import { Timeline } from 'antd'
import React, { useEffect, useState } from 'react'
import { isUUID } from '../../helpers'

interface Props {
  form: FormInstance
}

export default function ExpectedApplicationSteps({ form }: Props) {
  const fields = ['relApplicationToHostingProvider', 'relApplicationToOperationsProvider', 'relApplicationToSoftwareServiceProvider']
  const [messages, setMessages] = useState<any[]>([{
    children: 'Application factsheet will be created.'
  }])

  useEffect(() => {
    const uniqueNames: string[] = []

    for (let i = 0; i < fields.length; i++) {
      const field = fields[i]

      const value = form.getFieldValue(field)

      if (value && value.length > 0) {
        for (let j = 0; j < value.length; j++) {
          if (!isUUID(value[j]) && !uniqueNames.includes(value[j])) {
            uniqueNames.push(value[j])
          }
        }
      }
    }

    if (uniqueNames.length > 0) {
      const items = [...uniqueNames.map(n => ({ children: `Provider factsheet (${n}) will be created.` }))]

      setMessages([{
        children: 'Application factsheet will be created.'
      }, ...items])
    }
  }, [])

  return (<Timeline items={messages} />)
}
