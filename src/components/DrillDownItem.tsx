import type { JSX } from 'react'
import type { AppFactSheet, FactsheetMetaData } from '../models/data'
import { Tooltip } from 'antd'
import { factsheetsMetaData } from '../App'
import EditApplicationButton from './../components/EditApplicationButton'
import LifecycleMatchPopover from './../components/LifecycleMatchPopover'
import './DrillDownItem.css'

interface DrillDownItemProps {
  id: string
  name: string
  type: string
  showPane: (appId: string, type: string, drillDown: boolean) => void
  app?: AppFactSheet
  openModal: (app: AppFactSheet) => void
  sidePaneApp: string
}

export default function DrillDownItem({ app, id, name, type, showPane, openModal, sidePaneApp }: DrillDownItemProps): JSX.Element {
  let fsBGColor = ''
  factsheetsMetaData.find((element: FactsheetMetaData) => {
    if (element.type === type) {
      fsBGColor = element.bgColor
      return true
    }
    return false
  })
  const fsBG = {
    backgroundColor: fsBGColor,
    flexShrink: 0,
    color: 'white',
    height: '20px',
    width: '20px',
    display: 'inline-block',
    borderRadius: '2px',
    fontWeight: '700',
    lineHeight: '20px',
    padding: 0
  }
  return (
    <div className={`drill-down-wrapper ${sidePaneApp === `${app!.id}_drillDown` ? 'selectedApp' : ''}`} onClick={() => showPane(id, type, true)}>
      <div className="drill-down-inner-wrapper">
        <div className="icon-wrapper">
          {type === 'Application' && (
            <div className="edit-button-wrapper">
              <EditApplicationButton onClick={(event) => {
                event.stopPropagation()
                openModal(app!)
              }}
              />
            </div>
          )}
          <span className="row-icon" style={fsBG}>
            {lx.translateFactSheetType(type)?.charAt(0)}
          </span>
        </div>

        {app
          ? (
              <>
                {(app.updatedLifecycle || !app.validLifecycle) && (<LifecycleMatchPopover app={app} />)}
              </>
            )
          : undefined}
        <Tooltip title={name}>
          <p className="drill-down-name">
            {name}
          </p>
        </Tooltip>
      </div>
    </div>
  )
}
