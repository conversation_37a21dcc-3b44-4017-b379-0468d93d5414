import type { Application, LXDocument } from '../../model'
import { ExclamationCircleOutlined, InfoCircleOutlined, MailOutlined } from '@ant-design/icons'
import { Button, Modal, Popover, Space, Table, Tooltip } from 'antd'
import dayjs from 'dayjs'
import React from 'react'

interface Props {
  data: LXDocument[]
  deleteRow: (docId: string) => Promise<void>
  initTransfer: (application: Application, docId: string) => void
}

export default function DocumentsTable({ data, deleteRow, initTransfer }: Props) {
  const { confirm } = Modal

  const deleteDoc = (docId: string) => {
    confirm({
      title: 'Delete Factsheet Draft',
      icon: <ExclamationCircleOutlined />,
      content: 'Are you sure you want to delete this factsheet draft? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => deleteRow(docId)
    })
  }

  const columns: any = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '250px',
      render: (value: string, record: LXDocument) => {
        return (
          <Space>
            {value}
            {record.application?.comment?.length > 0 && (
              <Popover content={record.application.comment} title="Comment">
                <InfoCircleOutlined />
              </Popover>
            )}
          </Space>
        )
      }
    },
    {
      title: 'Type',
      key: 'fsType',
      render: (record: LXDocument) => {
        return record.application?.fsType === 'ITComponent' ? 'Desktop Software' : record.application?.fsType
      },
      width: '120px',
      filters: [
        {
          text: 'Application',
          value: 'Application'
        },
        {
          text: 'Desktop Software',
          value: 'Desktop Software'
        }
      ],
      onFilter: (value: string, record: LXDocument) =>
        (record.application?.fsType === 'ITComponent' ? 'Desktop Software' : record.application?.fsType).indexOf(value as string) === 0
    },
    {
      title: 'Created by',
      key: 'created_by',
      width: '230px',
      render: (record: LXDocument) => {
        const mail = record.application?.creator?.email
        const name = `${record.application?.creator?.firstName || ''} ${record.application?.creator?.lastName || ''}`

        if (name.trim().length > 0) {
          return (
            <Space>
              {name}
              {' '}
              <Popover
                title="E-Mail"
                content={(
                  <a
                    target="_blank"
                    onClick={() => lx.openLink(`mailto:${mail}`)}
                  >
                    {mail}
                  </a>
                )}
              >
                <MailOutlined />
              </Popover>
            </Space>
          )
        }
        else if (mail) { return <a target="_blank" onClick={() => lx.openLink(`mailto:${mail}`)}>{mail}</a> }
        else { return 'no user detected' }
      }
    },
    {
      title: 'Created at',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '150px',
      render: (text: string) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      title: 'Description',
      key: 'description',
      render: (record: LXDocument) => {
        if (record.application?.description.length > 100) {
          return (
            <Tooltip
              placement="top"
              title={record.application.description}
            >
              {record.application.description.slice(0, 100)}
              ...
            </Tooltip>
          )
        }
        else {
          return record.application?.description
        }
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '180px',
      render: (record: LXDocument) => (
        <Space>
          <Button
            size="small"
            onClick={() => initTransfer(JSON.parse(record.description), record.id)}
            type="primary"
          >
            Transfer
          </Button>
          <Button size="small" danger onClick={() => deleteDoc(record.id)}>Delete</Button>
        </Space>
      )
    }
  ]

  return (
    <Table
      dataSource={data}
      rowKey="id"
      size="small"
      scroll={{ y: 'calc(100vh - 230px)', x: 1200 }}
      pagination={false}
      columns={columns}
    />
  )
}
