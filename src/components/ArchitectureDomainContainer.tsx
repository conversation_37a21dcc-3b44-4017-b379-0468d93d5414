import React, { useState, useEffect } from "react";
import { ApplicationModel, DomainWrapper, PieModel } from "@app/model";
import { Card, Col, Row, Collapse, Space, Grid } from "antd";
import DomainPie from "@components/DomainPie";
import { ARCHITECTURE_DOMAIN_PIE } from "@app/contstants";

const { Panel } = Collapse;
const { useBreakpoint } = Grid;

interface Props {
    data: DomainWrapper[]
}

export default function ArchitectureDomainContainer({ data }: Props) {
    const screens = useBreakpoint();
    const [pieSize, setPieSize] = useState(220);

    useEffect(() => {
        if (screens.xxl) setPieSize(220);
        else if (screens.xl) setPieSize(200);
        else if (screens.lg) setPieSize(170);
        else if (screens.md) setPieSize(160);
        else if (screens.sm) setPieSize(140);
        else setPieSize(120);
    }, [screens]);

    const generateData = (apps: ApplicationModel[]) => {
        let tempArray: PieModel[] = JSON.parse(JSON.stringify(ARCHITECTURE_DOMAIN_PIE));

        let count = 0;
        apps.forEach((app) => {
            let tempIndex = compareSourcing(app.valueToTake);

            if (tempIndex !== null && tempIndex >= 0) {
                tempArray[tempIndex].value += 1;
                count++;
            }
        });

        return tempArray.filter((pieSection) => pieSection.value !== 0);
    }

    const compareSourcing = (sourcing: string) => {
        let tempIndex = null;

        switch (sourcing) {
            case "SaaS":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "CloudPaaS":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "CloudIaaS":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "saas":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "paas":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "iaas":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "Cloud");
                break;
            case "ExtAppHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            case "ExtPlatformHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            case "ExtInfraHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            case "desktopMobile":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "desktopMobile");
                break;
            case "internalHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "On-Premise");
                break;
            case "Rehosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "On-Premise");
                break;
            case "Replacement":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "On-Premise");
                break;
            case "ExternalInfraHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            case "ExternalPlatformHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            case "ExternalApplicationHosting":
                tempIndex = ARCHITECTURE_DOMAIN_PIE.findIndex((el) => el.type === "External Hosting");
                break;
            default:
                tempIndex = null;
        }

        return tempIndex;
    };

    const calculateOverallData = (domain: DomainWrapper) => {
        const allApps = domain.categories.flatMap(category =>
            category.domains.flatMap(d => d.apps)
        );

        return generateData(allApps);
    };

    return (
        <>
            {data.map(domain => (
                <Card
                    style={{ marginTop: "24px" }}
                    key={domain.date.toISOString()}
                    title={
                        domain.date.getDate().toString() +
                        "." +
                        (domain.date.getMonth() + 1).toString() +
                        "." +
                        domain.date.getFullYear().toString()
                    }
                >
                    <Space direction="vertical" style={{ width: '100%' }}>
                        {/* Overall als erstes Accordion */}
                        <Collapse defaultActiveKey={['0']}>
                            <Panel
                                header={
                                    <strong style={{ fontSize: '16px' }}>
                                        Overall
                                    </strong>
                                }
                                key="0"
                            >
                                <div style={{
                                    display: "flex",
                                    justifyContent: "center",
                                    padding: "16px",
                                    background: "#f5f5f5",
                                    borderRadius: "8px"
                                }}>
                                    <DomainPie
                                        data={calculateOverallData(domain)}
                                        showLegend={true}
                                        size={200}
                                        showPercentage={true}
                                    />
                                </div>
                            </Panel>
                        </Collapse>

                        {/* Kategorien als weitere Accordions */}
                        {domain.categories.map((category, index) => (
                            <Collapse
                                key={index}
                                defaultActiveKey={['1']}
                            >
                                <Panel
                                    header={
                                        <strong style={{ fontSize: '16px' }}>
                                            {category.categoryName}
                                        </strong>
                                    }
                                    key="1"
                                >
                                    <Row
                                        gutter={[16, 16]}
                                        align="middle"
                                    >
                                        {category.domains.map((d, index2) => (
                                            <Col
                                                key={index2}
                                                xs={24}
                                                sm={12}
                                                md={6}
                                                lg={4}
                                                xl={4}
                                                xxl={4}
                                            >
                                                <Card
                                                    size="small"
                                                    title={d.domainName}
                                                    bodyStyle={{
                                                        display: "flex",
                                                        justifyContent: "center",
                                                        padding: "8px"
                                                    }}
                                                >
                                                    <DomainPie
                                                        data={generateData(d.apps)}
                                                        size={pieSize}
                                                    />
                                                </Card>
                                            </Col>
                                        ))}
                                    </Row>
                                </Panel>
                            </Collapse>
                        ))}
                    </Space>
                </Card>
            ))}
        </>
    );
}

