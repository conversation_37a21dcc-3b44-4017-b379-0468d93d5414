import type { RadioChangeEvent } from 'antd'
import type { ApplicationModel } from '../utils/parser'
import { Radio, Skeleton } from 'antd'
import * as React from 'react'
import { useEffect, useState } from 'react'

interface Props {
  allData: ApplicationModel[]
  ressortFilterValue: string
  setRessortFilterValue: React.Dispatch<React.SetStateAction<string>>
}

interface Tag {
  id: string
  name: string
}

export const RessortFilter = ({ allData, ressortFilterValue, setRessortFilterValue }: Props) => {
  const [loading, setLoading] = useState(true)
  const [tags, setTags] = useState<Tag[]>([])

  const onChangeComponentSelection = (e: RadioChangeEvent) => {
    setRessortFilterValue(e.target.value)
  }

  useEffect(() => {
    loadRessorts().then()
  }, [])

  const loadRessorts = async () => {
    lx.executeGraphQL(`query {tagGroup(id:"9cfe8a6c-74f7-4298-8f55-2e6465244370"){tags{
      asList {id name}}}}`).then((result) => {
      let parsed: Tag[] = [...result.tagGroup.tags.asList.map((t: any) => ({ id: t.id, name: t.name }))]

      // Remove BIZ TAGS
      parsed = parsed.filter((t: Tag) => !t.name.includes('BIZ'))
      parsed.push({
        id: 'BIZ',
        name: 'BIZ'
      })
      // REMOVE IT PREFIX
      parsed = parsed.map((t: Tag) => ({ id: t.id, name: t.name.replace('IT ', '') }))

      setTags(parsed)
      setLoading(false)
    }).catch(error => lx.showToastr('error', error.message))
  }

  return (
    <>
      {loading
        ? <Skeleton active />
        : (
            <Radio.Group value={ressortFilterValue} buttonStyle="solid" onChange={onChangeComponentSelection}>
              <Radio.Button value="all">
                All applications:
                {allData.length}
              </Radio.Button>
              {tags.map(tag => (
                <Radio.Button
                  key={tag.id}
                  value={tag.name}
                >
                  {tag.name}
                  :
                  {allData.filter((a: ApplicationModel) => a.ITRessort.name.toLowerCase() === tag.name.toLowerCase()).length}
                </Radio.Button>
              ))}
              <Radio.Button value="not assigned">
                not
                assigned:
                {allData.filter((a: ApplicationModel) => a.ITRessort.name.toLowerCase() === 'not assigned').length}
              </Radio.Button>
            </Radio.Group>
          )}
      {' '}

    </>
  )
}
