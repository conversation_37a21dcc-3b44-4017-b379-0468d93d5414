import type { Application, CorpUnit, Value } from '@app/models'
import { Col, Flex, Row, Select, Skeleton, Space, Splitter } from 'antd'
import Search from 'antd/es/input/Search'
import Title from 'antd/es/typography/Title'
import React, { useEffect, useState } from 'react'
import ExportButton from './../../components/core/ExportButton'
import {
  BUSINESS_AREA_AT,
  BUSINESS_AREA_CAS,
  BUSINESS_AREA_DE,
  BUSINESS_AREA_ES,
  BUSINESS_AREA_FR,
  BUSINESS_AREA_ITA
} from './../../constants'
import {
  exportGroupApplicationsByMarketUnit,
  exportMarketUnitSpecificApplications,
  sortRecursively
} from './../../helper'
import { useLeanIX } from './../../hooks/useLeanIX'
import useWindowDimensions from './../../hooks/useWindowDimensions'
import GroupApplicationUsedByMarketUnit from './GroupApplicationUsedByMarketUnit'
import MarketUnitSpecificApplications from './MarketUnitSpecificApplications'

interface Props {
  data: Application[]
  corpUnit: CorpUnit
  setCorpUnits: (corpUnit: CorpUnit) => void
  loading: boolean
}

export default function MarketUnitOverviewContainer({ data, corpUnit, setCorpUnits, loading }: Props) {
  const { getPlatforms } = useLeanIX()
  const { height } = useWindowDimensions()
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [filteredForGroupApplications, setFilteredForGroupApplications] = useState<Application[]>([])
  const [searchText, setSearchText] = useState('')
  const [searchText2, setSearchText2] = useState('')

  const [selectedPlatform, setSelectedPlatform] = useState<string>('all')
  const [platforms, setPlatforms] = useState<Value[]>([])

  const getBusinessAreaId = (cUnit: string): string => {
    if (cUnit === 'AT') { return BUSINESS_AREA_AT }
    if (cUnit === 'DE') { return BUSINESS_AREA_DE }
    if (cUnit === 'FR') { return BUSINESS_AREA_FR }
    if (cUnit === 'ITA') { return BUSINESS_AREA_ITA }
    if (cUnit === 'ES') { return BUSINESS_AREA_ES }
    if (cUnit === 'CAS') { return BUSINESS_AREA_CAS }

    return ''
  }

  useEffect(() => {
    if (lx.currentSetup.settings) {
      getPlatforms().then(p => setPlatforms([{ value: 'all', label: 'All' }, { value: 'no', label: 'No' }, ...p]))
    }
  }, [lx.currentSetup])

  useEffect(() => {
    if (data && corpUnit) {
      let filtered: Application[] = [...data]

      let grouped = [...data].filter(a => a.businessAreas.filter(b => b.value === getBusinessAreaId(corpUnit)).length > 0 && (a.corpUnit === 'CF' || a.corpUnit === 'CH'))

      grouped.map((app) => {
        if (app.children && app.children.length > 0) {
          app.children = app.children.filter(c => c.businessAreas.filter(b => b.value === getBusinessAreaId(corpUnit)).length > 0)
        }
      })

      if (searchText2.length > 0) {
        grouped = grouped.filter(app => app.name.toLowerCase().includes(searchText2.toLowerCase()))
      }

      grouped = sortRecursively(grouped)

      setFilteredForGroupApplications(grouped)

      filtered = filtered.filter((app: Application) => app.corpUnit === corpUnit)

      if (selectedPlatform != 'all') {
        if (selectedPlatform == 'no') {
          filtered = filtered.filter((app: Application) => app.platform === '')
        }
        else {
          filtered = filtered.filter((app: Application) => app.platform === selectedPlatform)
        }
      }

      if (searchText.length > 0) {
        filtered = filtered.filter(app => app.name.toLowerCase().includes(searchText.toLowerCase()))
      }

      // Sortierung hinzufügen
      filtered = sortRecursively(filtered)

      setFilteredApplications(filtered)
    }
  }, [data, corpUnit, selectedPlatform, searchText, searchText2])

  return (
    <>
      <Row gutter={0}>
        <Col span={24}>
          <Flex justify="space-between" align="center">
            <Flex justify="flex-start" align="baseline">
              <Title style={{ width: '310px' }} level={4}>Market Unit Specific Applications</Title>
              <Search
                placeholder="Search..."
                value={searchText}
                onChange={(e) => { setSearchText(e.target.value) }}
                style={{ width: '200px' }}
                className="search-input"
              />
            </Flex>

            <div>
              <Space style={{ paddingLeft: '24px' }}>
                <span>Platform:</span>
                <Select
                  defaultValue="all"
                  options={platforms}
                  onChange={value => setSelectedPlatform(value)}
                  style={{ width: '200px' }}
                />
                <ExportButton download={xlsx => exportMarketUnitSpecificApplications(xlsx, filteredApplications)} />
              </Space>
            </div>
          </Flex>
          <Splitter layout="vertical" style={{ height: height - 150, boxShadow: '0 0 10px rgba(0, 0, 0, 0.01)' }}>
            <Splitter.Panel>
              <div style={{
                height: '100%', // oder eine spezifische Höhe
                display: 'flex',
                flexDirection: 'column'
              }}
              >
                <div style={{ flex: 1 }}>
                  {loading
                    ? <Skeleton active />
                    : <MarketUnitSpecificApplications data={filteredApplications} />}
                </div>
              </div>
            </Splitter.Panel>
            <Splitter.Panel>
              <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <div style={{ flexShrink: 0 }}>
                  <Flex justify="space-between" align="center" style={{ marginBottom: '8px' }}>
                    <Flex justify="flex-start" align="baseline">
                      <Title style={{ width: '390px' }} level={4}>Group Applications Used By Market Unit</Title>
                      <Search
                        placeholder="Search..."
                        value={searchText2}
                        onChange={(e) => { setSearchText2(e.target.value) }}
                        style={{ width: '200px' }}
                        className="search-input"
                      />
                    </Flex>
                    <ExportButton download={xlsx => exportGroupApplicationsByMarketUnit(xlsx, filteredForGroupApplications)} />
                  </Flex>

                </div>
                <div style={{ flex: 1, overflow: 'auto' }}>
                  {loading
                    ? <Skeleton active />
                    : (
                        <GroupApplicationUsedByMarketUnit
                          data={filteredForGroupApplications}
                        />
                      )}
                </div>
              </div>
            </Splitter.Panel>
          </Splitter>
        </Col>
      </Row>
    </>
  )
}
