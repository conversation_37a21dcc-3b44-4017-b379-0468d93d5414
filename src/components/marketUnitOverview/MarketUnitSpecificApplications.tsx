import type { Application, ValueFilter } from '@app/models'
import type { BadgeProps } from 'antd'
import { Badge, Button, Space, Table, Tag } from 'antd'
import React from 'react'
import { SERVICE_NOW_URL } from './../../constants'

interface Props {
  data: Application[]
}

export default function MarketUnitSpecificApplications({ data }: Props) {
  const columns = [
    {
      title: 'Domain',
      width: '250px',
      fixed: 'left',
      dataIndex: ['domain', 'label'],
      key: 'domain.label',
      sorter: (a: Application, b: Application) =>
        (a.domain?.label || '').localeCompare(b.domain?.label || ''),
      filters: Array.from(new Set(data.map(item => item.domain?.label)))
        .filter((label): label is string => Boolean(label))
        .map((domain): ValueFilter => ({ text: domain, value: domain })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.domain?.label === value,
      render: (_: unknown, record: Application) =>
        (<span>{record.domain?.label}</span>)
    },
    {
      title: 'Application',
      width: '300px',
      fixed: 'left',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Application, b: Application) =>
        a.name.localeCompare(b.name),
      filters: Array.from(new Set(data.map(item => item.name)))
        .map((name): ValueFilter => ({ text: name, value: name })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.name === value,
      render: (_: unknown, record: Application) => (
        <a
          onClick={() => lx.openLink(`/factsheet/Application/${record.id}`)}
          target="_blank"
        >
          {record.name}
        </a>
      )
    },
    {
      title: 'Level',
      width: '80px',
      dataIndex: 'level',
      key: 'level',
      sorter: (a: Application, b: Application) =>
        a.level - b.level,
      filters: Array.from(new Set(data.map(item => item.level)))
        .map((level): ValueFilter => ({
          text: level.toString(),
          value: level.toString()
        })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.level.toString() === value.toString()
    },
    {
      title: 'Lifecycle',
      width: '120px',
      dataIndex: 'lifecycle',
      key: 'lifecycle',
      sorter: (a: Application, b: Application) =>
        a.lifecycle.localeCompare(b.lifecycle),
      filters: Array.from(new Set(data.map(item => item.lifecycle)))
        .map((lifecycle): ValueFilter => ({
          text: lx.translateFieldValue('Application', 'lifecycle', lifecycle),
          value: lifecycle
        })),
      onFilter: (value: boolean | React.Key, record: Application): boolean =>
        record.lifecycle === value,
      render: (text: string, record: Application) => {
        const statusMap: Record<string, { status: BadgeProps['status'], text: string }> = {
          active: {
            status: 'success',
            text: lx.translateFieldValue('Application', 'lifecycle', 'active')
          },
          endOfLife: {
            status: 'error',
            text: lx.translateFieldValue('Application', 'lifecycle', 'endOfLife')
          },
          phaseIn: {
            status: 'default',
            text: lx.translateFieldValue('Application', 'lifecycle', 'phaseIn')
          },
          phaseOut: {
            status: 'warning',
            text: lx.translateFieldValue('Application', 'lifecycle', 'phaseOut')
          },
          plan: {
            status: 'default',
            text: lx.translateFieldValue('Application', 'lifecycle', 'plan')
          }
        }

        const config = statusMap[record.lifecycle]
        if (config) {
          return <Badge status={config.status} text={config.text} />
        }
        return text
      }
    },

    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      width: '120px',
      render: (_: unknown, record: Application) => lx.translateFieldValue('Application', 'plattform', record.platform)
    },
    {
      title: 'Service BL Approach',
      dataIndex: 'serviceBaseLineApproach',
      key: 'serviceBaseLineApproach',
      width: '120px'
    },
    {
      title: 'Comment',
      dataIndex: 'serviceBaseLineComment',
      key: 'serviceBaseLineComment',
      width: '120px'
    },
    {
      title: 'SnowID',
      dataIndex: 'snowId',
      key: 'snowId',
      width: '120px',
      render: (_: unknown, record: Application) => {
        if (record.snowId) {
          return (
            <Button type="link" onClick={() => lx.openLink(`${SERVICE_NOW_URL}${record.snowId}`)}>Open</Button>)
        }
        else { return ('-') }
      }
    },
    {
      title: 'Project',
      dataIndex: 'project',
      key: 'project',
      width: '200px',
      render: (_: unknown, record: Application) => {
        if (record.projects.length > 0) {
          return (
            <div style={{ width: '200px', overflowX: data.length > 2 ? 'scroll' : 'unset', scrollbarWidth: 'thin' }}>
              <Space>{record.projects.map(p => (<Tag key={p.label}>{p.label}</Tag>))}</Space>
            </div>
          )
        }
        return ('')
      }
    }

  ]

  return (
    <Table
      columns={columns as any}
      rowKey="id"
      sticky={{ offsetHeader: 0 }}
      scroll={{ y: '100%', x: 'auto' }}
      style={{ display: 'flex' }}
      pagination={false}
      dataSource={data}
    />
  )
}
