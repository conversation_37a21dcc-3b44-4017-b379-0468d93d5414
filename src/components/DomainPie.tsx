import type { PieModel } from '@app/model'
import { ARCHITECTURE_DOMAIN_PIE } from '@app/contstants'
import { useEffect, useState } from 'react'
import { <PERSON>, Legend, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts'

interface Props {
  data: PieModel[]
  showLegend?: boolean
  size?: number
  showPercentage?: boolean
}

export default function DomainPie({ data, showLegend = false, size = 180, showPercentage = false }: Props) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const [labelFontSize, setLabelFontSize] = useState(12)

  useEffect(() => {
    if (size <= 140) { setLabelFontSize(8) }
    else if (size <= 180) { setLabelFontSize(10) }
    else { setLabelFontSize(12) }
  }, [size])

  const width = showLegend ? size * 2.5 : 250
  const height = showLegend ? size * 1.4 : size
  const outerRadius = showLegend ? 100 : 45


  const RADIAN = Math.PI / 180
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    outerRadius,
    percent,
    value
  }: any) => {
    const scaleFactor = size / 220

    const radius = outerRadius * (1.7 * scaleFactor)
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    const lineEndRadius = outerRadius * (1.4 * scaleFactor)
    const lineEndX = cx + lineEndRadius * Math.cos(-midAngle * RADIAN)
    const lineEndY = cy + lineEndRadius * Math.sin(-midAngle * RADIAN)

    const outerX = cx + outerRadius * Math.cos(-midAngle * RADIAN)
    const outerY = cy + outerRadius * Math.sin(-midAngle * RADIAN)

    if (size < 120 || !value) {
      return null
    }

    let labelText = value.toString()
    let labelWidth = value.toString().length * 8 + 5

    if (showPercentage) {
      const percentageText = `${(percent * 100).toFixed(2)}%`
      labelText = `${value} | ${percentageText}`
      labelWidth = labelText.length * 7 + 5
    }

    return (
      <g>
        {/* Verbindungslinie vom Kreisrand zur Zahl */}
        <line
          x1={outerX}
          y1={outerY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Die Zahl - mit einer kleinen Box für bessere Lesbarkeit */}
        <g>
          <rect
            x={x > cx ? x - 2 : x - (showPercentage ? labelWidth - 5 : 20)}
            y={y - 9}
            width={labelWidth}
            height={18}
            fill="white"
            fillOpacity={0.8}
            rx={3}
            ry={3}
          />
          <text
            x={x}
            y={y}
            fill="#333"
            textAnchor={x > cx ? 'start' : 'end'}
            dominantBaseline="central"
            fontSize={labelFontSize}
            fontWeight="bold"
          >
            {labelText}
          </text>
        </g>
      </g>
    )
  }

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index)
  }

  const onPieLeave = () => {
    setActiveIndex(null)
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #999',
          padding: '10px',
          borderRadius: '4px',
          boxShadow: '0 2px 5px rgba(0,0,0,0.15)'
        }}
        >
          <p style={{ margin: 0 }}><strong>{payload[0].name}</strong></p>
          <p style={{ margin: 0 }}>
            Anzahl:
            {payload[0].value}
          </p>
        </div>
      )
    }
    return null
  }

  if (!data || data.length === 0) {
    return (
      <div
        style={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '12px'
        }}
      >
        No data available
      </div>
    )
  }

  return (
    <div style={{ width, height }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          {showLegend && (
            <Legend
              layout="horizontal"
              align="center"
              verticalAlign="bottom"
              iconSize={size < 180 ? 8 : 10}
              iconType="circle"
              wrapperStyle={{
                fontSize: size < 180 ? 10 : 12,
                paddingTop: 10
              }}
            />
          )}
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine
            label={renderCustomizedLabel}
            outerRadius={outerRadius}
            fill="#8884d8"
            dataKey="value"
            nameKey="type"
            onMouseEnter={onPieEnter}
            onMouseLeave={onPieLeave}
          >
            {data.map((entry, index) => {
              const matchingEntryIndex = ARCHITECTURE_DOMAIN_PIE.findIndex(e => e.type === entry.type)
              const color = matchingEntryIndex >= 0 ? ARCHITECTURE_DOMAIN_PIE[matchingEntryIndex].color : '#8884d8'
              return (
                <Cell
                  key={`cell-${index}`}
                  fill={color}
                  stroke={activeIndex === index ? '#000' : 'none'}
                  strokeWidth={activeIndex === index ? 2 : 0}
                />
              )
            })}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
