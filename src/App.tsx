// Import interfaces
import type {
  AppClusters,
  AppFactSheet,
  CustomMilestoneFilter,
  CustomMilestones,
  CustomState,
  DisplayMode,
  FactsheetMetaData,
  MilestonesFormData
} from './models/data'
import type { TimelineDates } from './models/timeline.models'
import { Alert, Skeleton } from 'antd'

import Search from 'antd/es/input/Search'
// Import libraries
import { useEffect, useRef, useState } from 'react'

import { CustomFilter } from './components/CustomFilter'
import Roadmap from './components/Roadmap'
// Import contexts
import { appsDrillDownsContext } from './contexts/Context'
import { groupAppsByClusters, loadTags } from './helpers/Clustering'
import { getMarkerLocation, prepareTimelineData } from './helpers/CreateTimelineData'
import { fetchAppRelations, getAppRelationsDict } from './helpers/FetchAppRelations'

import { customMilestonesAttributes } from './helpers/FetchAttributes'

import { transformTagsListToDict } from './helpers/FormatData'
import { parseLifecycle } from './helpers/ParseLifecycle'
// Import configs and helper functions
import { configureSettingsFields } from './helpers/ReportSettings'
import '@leanix/reporting'
// Import styles
import './App.css'
import './style.scss'
import FacetFilter = lxr.FacetFilter
import FacetKeyOperator = lxr.FacetKeyOperator
import ReportFacetsConfig = lxr.ReportFacetsConfig

// Global variables
export const appRelationsMap = {}
export let factsheetsMetaData: FactsheetMetaData[] = []

const getInitialReportSettingsValues = (): lxr.FormModalValues => {
  return ({
    clusterBy: 'rel_relApplicationToDomain',
    drillDown: 'relToSuccessor',
    startDate: `${new Date().getFullYear() - 1}-01-01`,
    endDate: `${new Date().getFullYear() + 6}-01-01`
  })
}

const getEmptyState = (): CustomState => {
  return {
    zoomLevel: 0.7,
    sortOrder: 'alphabetical',
    showLabels: false,
    currentSettings: getInitialReportSettingsValues()
  }
}

export default function App() {
  // Define states for report settings
  const [reportSettingsValues, setReportSettingsValues] = useState<lxr.FormModalValues>(getInitialReportSettingsValues())
  const [timelineDates, _setTimelineDates] = useState<TimelineDates>({
    startDate: reportSettingsValues.startDate,
    endDate: reportSettingsValues.endDate
  })
  const [appsRawData, setAppsRawData] = useState<AppFactSheet[]>([])
  const [apps, setApps] = useState<AppFactSheet[] | AppClusters>([])
  const [years, setYears] = useState<number[]>([])
  const [showLabels, setShowLabels] = useState(getEmptyState().showLabels)
  const [yearWidth, _setYearWidth] = useState(400)
  const [zoomLevel, setZoomLevel] = useState(getEmptyState().zoomLevel)
  const [sortOrder, setSortOrder] = useState(getEmptyState().sortOrder)
  const [todayMarker, setTodayMarker] = useState({ startDate: -42 })
  const [allTags, setAllTags] = useState()
  const [tagColors, setTagColors] = useState()
  // const [reportConfig, setReportConfig] = useState<lxr.ReportConfiguration>({})
  const [appRelations, setAppRelations] = useState({})

  const [filters, setFilters] = useState<any[]>([])
  const [filteredIds, setFilteredIds] = useState<string[]>([])

  const reportSettingsFields = useRef({})

  const [loading, setLoading] = useState(true)

  const [searchText, setSearchText] = useState('')

  const [sidePaneApp, setSidePaneApp] = useState<string>('')

  // const [selectedProjects, setSelectedProjects] = useState<string[]>([]);

  const [currentUser, setCurrentUser] = useState<lxr.ReportUser | undefined>(undefined)
  // const [userIds, setUserIds] = useState<string[]>([]);

  const [displayMode, setDisplayMode] = useState<DisplayMode>('all')

  const [customFilters, setCustomFilters] = useState<CustomMilestoneFilter>({
    selectedProjects: [],
    userIds: []
  })

  const updateAppRelations = (app: AppFactSheet & CustomMilestones) => {
    let newActiveDate: string | undefined
    let newPhaseOutDate: string | undefined

    if (app?.planningGoLiveMilestone1.length > 0) {
      newActiveDate = app.planningGoLiveMilestone1
    }
    if (app?.planningSwitchOffMilestone4.length > 0) {
      newPhaseOutDate = app.planningSwitchOffMilestone4
    }

    // console.log("UPDATE APP RELATIONS", app.id, newActiveDate,newPhaseOutDate)

    const findRelationsWithDisplayName = (data: {
      [id: string]: { [relation: string]: Array<{ displayName: string, [key: string]: any }> }
    }, factSheetId: string): Array<{ id: string, relations: string[] }> => {
      const results: Array<{ id: string, relations: string[] }> = []

      for (const id in data) {
        if (data.hasOwnProperty(id)) {
          const relationships = data[id]
          const foundRelations: string[] = []

          for (const key in relationships) {
            if (relationships.hasOwnProperty(key)) {
              const relationArray = relationships[key]
              if (relationArray.some(item => item.id === factSheetId)) {
                foundRelations.push(key)
              }
            }
          }
          if (foundRelations.length > 0) {
            results.push({ id, relations: foundRelations })
          }
        }
      }
      return results
    }

    const items = findRelationsWithDisplayName(appRelations, app.id)
    // console.log("ITEMS TO UPDATE", items)

    setAppRelations((prevState) => {
      const newState = JSON.parse(JSON.stringify(prevState))

      items.forEach((item) => {
        item.relations.forEach((rel) => {
          // Stelle sicher, dass diese Beziehung existiert
          if (newState[item.id] && newState[item.id][rel]) {
            const factSheets = newState[item.id][rel]

            factSheets.forEach((factSheet: any) => {
              if (app.id === factSheet.id) {
                // Direkte Bearbeitung der lifeCyclePhases im newState
                const lifeCyclePhases = factSheet.lifecycle.phases

                lifeCyclePhases.forEach((p: { phase: string, startDate: string }) => {
                  if (p.phase === 'active' && newActiveDate !== undefined) {
                    p.startDate = newActiveDate
                  }
                  if (p.phase === 'phaseOut' && newPhaseOutDate !== undefined) {
                    p.startDate = newPhaseOutDate
                  }
                })

                if (!lifeCyclePhases.some((p: {
                  phase: string
                  startDate: string
                }) => p.phase === 'active') && newActiveDate) {
                  lifeCyclePhases.push({
                    phase: 'active',
                    startDate: newActiveDate
                  })
                }
                if (!lifeCyclePhases.some((p: {
                  phase: string
                  startDate: string
                }) => p.phase === 'phaseOut') && newPhaseOutDate) {
                  lifeCyclePhases.push({
                    phase: 'phaseOut',
                    startDate: newPhaseOutDate
                  })
                }

                // Filtern, anstatt neu zuzuweisen
                factSheet.lifecycle.phases = lifeCyclePhases.filter((p: {
                  phase: string
                  startDate: string
                }) => {
                  if (p.phase === 'active' && newActiveDate === undefined) {
                    return false
                  }
                  if (p.phase === 'phaseOut' && newPhaseOutDate === undefined) {
                    return false
                  }
                  return true
                })

                const milestones = ['GoLive', 'SwitchOff']
                const comments = ['GoLiveComment', 'SwitchOffComment']

                for (let i = 1; i <= 4; i++) {
                  milestones.forEach((milestone) => {
                    factSheet[`planning${milestone}Milestone${i}`] = app[`planning${milestone}Milestone${i}`]
                  })
                  comments.forEach((comment) => {
                    factSheet[`planning${comment}M${i}`] = app[`planning${comment}M${i}`]
                  })
                }
              }
              else {
                // console.log("THIS RELATION NEEDS NO UPDATE", factSheet)
              }
            })
          }
        })
      })

      // console.log("NEW STATE", newState)

      return newState
    })
  }

  const updateApplication = (application: AppFactSheet, formData: MilestonesFormData | undefined) => {
    // console.log("[CHANGED APP] application start", application)

    if (formData) {
      const apps = appsRawData.filter(a => a.id === application.id)
      // console.log("[CHANGED APP] apps found", apps)

      if (apps.length > 0) {
        // console.log("[CHANGED APP] application", apps);

        const app = JSON.parse(JSON.stringify(apps[0]))

        const changedApp: AppFactSheet = {
          ...app,
          planningGoLiveCommentM1: formData.planningGoLiveCommentM1,
          planningGoLiveCommentM2: formData.planningGoLiveCommentM2,
          planningGoLiveCommentM3: formData.planningGoLiveCommentM3,
          planningGoLiveCommentM4: formData.planningGoLiveCommentM4,
          planningSwitchOffCommentM1: formData.planningSwitchOffCommentM1,
          planningSwitchOffCommentM2: formData.planningSwitchOffCommentM2,
          planningSwitchOffCommentM3: formData.planningSwitchOffCommentM3,
          planningSwitchOffCommentM4: formData.planningSwitchOffCommentM4,

          planningSwitchOffMilestone1: formData.planningSwitchOffMilestone1,
          planningSwitchOffMilestone2: formData.planningSwitchOffMilestone2,
          planningSwitchOffMilestone3: formData.planningSwitchOffMilestone3,
          planningSwitchOffMilestone4: formData.planningSwitchOffMilestone4,

          planningGoLiveMilestone1: formData.planningGoLiveMilestone1,
          planningGoLiveMilestone2: formData.planningGoLiveMilestone2,
          planningGoLiveMilestone3: formData.planningGoLiveMilestone3,
          planningGoLiveMilestone4: formData.planningGoLiveMilestone4

        }

        if (!changedApp.lifecycle) {
          changedApp.lifecycle = {
            phases: []
          }
        }

        // UPDATE Lifecycle

        const updateLifecyclePhase = (appLifecycle: any, phaseType: string, milestoneDate: string, oldMilestoneDate: string) => {
          const currentDatePhase = appLifecycle.phases.filter((phase: {
            phase: string
            startDate: string
          }) => phase.phase === phaseType)

          if (milestoneDate !== oldMilestoneDate || (currentDatePhase.length > 0 && currentDatePhase[0].startDate !== milestoneDate)) {
            if (milestoneDate === '') {
              appLifecycle.phases = appLifecycle.phases.filter((phase: {
                phase: string
                startDate: string
              }) => phase.phase !== phaseType)
            }
            else {
              const phaseExists = appLifecycle.phases.some((phase: {
                phase: string
                startDate: string
              }) => phase.phase === phaseType)

              if (phaseExists) {
                appLifecycle.phases = appLifecycle.phases.map((phase: {
                  phase: string
                  startDate: string
                }) => {
                  if (phase.phase === phaseType) {
                    return { ...phase, startDate: milestoneDate }
                  }
                  return phase
                })
              }
              else {
                appLifecycle.phases.push({
                  phase: phaseType,
                  startDate: milestoneDate
                })
              }
            }
          }
        }

        // active
        updateLifecyclePhase(changedApp.lifecycle, 'active', formData.planningGoLiveMilestone1, app.planningGoLiveMilestone1)
        // end of life
        updateLifecyclePhase(changedApp.lifecycle, 'phaseOut', formData.planningSwitchOffMilestone4, app.planningSwitchOffMilestone4)

        // original lifecycle

        if (changedApp.originalLifecycle) {
          // active
          updateLifecyclePhase(changedApp.originalLifecycle, 'active', formData.planningGoLiveMilestone1, app.planningGoLiveMilestone1)
          // end of life
          updateLifecyclePhase(changedApp.originalLifecycle, 'phaseOut', formData.planningSwitchOffMilestone4, app.planningSwitchOffMilestone4)
        }

        // console.log("[CHANGED APP] final", changedApp)

        const appToParseLifeCycle = [changedApp]

        // NEW
        parseLifecycle(appToParseLifeCycle)

        const updatedApps = appsRawData.map(app =>
          app.id === appToParseLifeCycle[0].id ? appToParseLifeCycle[0] : app
        )

        // UPDATE APP RELATIONS!
        updateAppRelations(changedApp)

        setAppsRawData(updatedApps)
      }
      else {
        // ONLY APP RELATION UPDATE

        // console.log("APP RELATION UPDATE BEFORE", application)

        const updatedApp: any = {
          ...application,
          planningGoLiveCommentM1: formData.planningGoLiveCommentM1,
          planningGoLiveCommentM2: formData.planningGoLiveCommentM2,
          planningGoLiveCommentM3: formData.planningGoLiveCommentM3,
          planningGoLiveCommentM4: formData.planningGoLiveCommentM4,

          planningSwitchOffCommentM1: formData.planningSwitchOffCommentM1,
          planningSwitchOffCommentM2: formData.planningSwitchOffCommentM2,
          planningSwitchOffCommentM3: formData.planningSwitchOffCommentM3,
          planningSwitchOffCommentM4: formData.planningSwitchOffCommentM4,

          planningSwitchOffMilestone1: formData.planningSwitchOffMilestone1,
          planningSwitchOffMilestone2: formData.planningSwitchOffMilestone2,
          planningSwitchOffMilestone3: formData.planningSwitchOffMilestone3,
          planningSwitchOffMilestone4: formData.planningSwitchOffMilestone4,

          planningGoLiveMilestone1: formData.planningGoLiveMilestone1,
          planningGoLiveMilestone2: formData.planningGoLiveMilestone2,
          planningGoLiveMilestone3: formData.planningGoLiveMilestone3,
          planningGoLiveMilestone4: formData.planningGoLiveMilestone4

        }

        // console.log("APP RELATION UPDATE AFTER", updatedApp);

        updateAppRelations(updatedApp)
      }
    }
    else {
      // RESET TO OLD STATE

      // console.log("RESET TO OLD STATE")

      const appToReset: AppFactSheet = JSON.parse(JSON.stringify(application))

      appToReset.lifecycle.phases.forEach((obj: any) => {
        if (obj.phaseDate) {
          obj.startDate = obj.phaseDate
          delete obj.label
          delete obj.phaseDate
        }
      })

      appToReset.lifecycle.phases = appToReset.lifecycle.phases.filter(p => !p.phase.includes('_custom'))

      const appToParseLifeCycle = [appToReset]
      // NEW
      parseLifecycle(appToParseLifeCycle)

      const updatedApps = appsRawData.map(app =>
        app.id === appToParseLifeCycle[0].id ? appToParseLifeCycle[0] : app
      )

      setAppsRawData(updatedApps)
    }
  }

  const refreshApplications = async () => {
    if (!filters) {
      lx.showToastr('error', 'Refresh not possible')
    }
    else {
      // setLoading(true)
      // setAppsRawData([])
      const query = `
   query apps($appFilter: FilterInput!) {
  allFactSheets(filter: $appFilter) {
    totalCount
    edges {
      node {
        id
        type
        displayName
        ... on Application {
          lifecycle {
            asString
            phases {
              phase
              startDate
            }
          }
          tags {
            id
            name
            tagGroup {
              id
              name
            }
          }
          planningGoLiveMilestone1
          planningGoLiveMilestone2
          planningGoLiveMilestone3
          planningGoLiveMilestone4
          planningGoLiveCommentM1
          planningGoLiveCommentM2
          planningGoLiveCommentM3
          planningGoLiveCommentM4
          planningSwitchOffMilestone1
          planningSwitchOffMilestone2
          planningSwitchOffMilestone3
          planningSwitchOffMilestone4
          planningSwitchOffCommentM1
          planningSwitchOffCommentM2
          planningSwitchOffCommentM3
          planningSwitchOffCommentM4
        }
      }
    }
  }
}

`

      const basicFilter = {
        appFilter: {
          facetFilters: filters,
          ids: filteredIds
        }
      }

      // MAKE QUERY GRAPHQL
      const data = await lx.executeGraphQL(query, JSON.stringify(basicFilter))
      const parsed = [...data.allFactSheets.edges.map((app: any) => ({ ...app.node }))]

      const apps_ = transformTagsListToDict(parsed)
      setAppsRawData(apps_)
      // setLoading(false)
    }
  }

  const toggleLabels = () => {
    // #save-state
    setShowLabels((prevShowLabels) => {
      const showLabels = !prevShowLabels

      const updatedState: CustomState = {
        ...lx.latestPublishedState,
        showLabels
      }

      lx.publishState(updatedState)
      return showLabels
    })
  }

  useEffect(() => {
    async function updateData() {
      /*
             * Update application data evertytime configuration get changed or filters are applied
             */
      if (appsRawData.length > 0) {
        let filteredRaw: AppFactSheet[] = [...appsRawData]

        if (searchText.length > 0) {
          filteredRaw = appsRawData.filter(app => app.displayName.toLowerCase().includes(searchText.toLowerCase()))
        }

        const startYear = new Date(reportSettingsValues.startDate as string).getUTCFullYear()
        const endYear = new Date(reportSettingsValues.endDate as string).getUTCFullYear() + 1

        setYears(new Array(endYear - startYear).fill(0).map((_n, i) => startYear + i))

        let apps: AppFactSheet[] | AppClusters = prepareTimelineData(JSON.parse(JSON.stringify(filteredRaw)), {
          startDate: reportSettingsValues.startDate,
          endDate: reportSettingsValues.endDate
        }, yearWidth, sortOrder)
        // console.log("APPS AFTER prepareTimelineData()", apps);

        setTodayMarker({
          startDate: getMarkerLocation({
            startDate: reportSettingsValues.startDate as string,
            endDate: reportSettingsValues.endDate as string
          }, yearWidth)
        })

        if (reportSettingsValues.clusterBy) {
          apps = groupAppsByClusters(apps, appRelations, allTags, reportSettingsValues.clusterBy as string)
        }

        setApps(apps)
      }
      else {
        setApps([])
      }
    }

    updateData()
  }, [appsRawData, reportSettingsValues, yearWidth, sortOrder, filters, filteredIds, searchText])

  const getReportConfig = (setupInfo: lxr.ReportSetup): lxr.ReportConfiguration => {
    const customState: CustomState = setupInfo.savedState?.customState ?? getEmptyState()
    // console.log('setupInfo.savedState.customState:', setupInfo.savedState?.customState)
    console.log('customState: ', customState)

    setReportSettingsValues(customState.currentSettings)
    setShowLabels(customState.showLabels)

    return {
      menuActions: {
        configureCallback: openSettingsForm,
        showConfigure: true
      },
      reportViewFactSheetType: 'Application',
      reportViewCallback() {
      },
      allowTableView: true,
      facets: [
        {
          key: 'application',
          fixedFactSheetType: 'Application',
          attributes: ['id', 'type', 'displayName', ' lifecycle {asString phases {phase startDate } }', ...customMilestonesAttributes, 'tags { id name tagGroup { id name } }'],
          async callback(apps: AppFactSheet[]) {
            apps = transformTagsListToDict(apps)

            parseLifecycle(apps)

            setAppsRawData(apps)
          },
          defaultFilters: [
            {
              facetKey: '021f6826-8798-415a-861b-271099c5f475',
              keys: ['88bbf576-ce34-4d9e-aa0c-c0e5e7e9873e']
            }
            /* {
                            facetKey: "Subscriptions",
                            operator: "OR" as any,
                            keys: [
                                "643ee141-dbec-4458-a14b-4b105e365aa2"
                            ],
                            subscriptionFilter: {
                                "type": "RESPONSIBLE",
                                "roleId": "d2096746-2a62-4cd9-b6a2-307bb4b5a2a5"
                            }
                        } */
          ],
          facetFiltersChangedCallback(facet) {
            setFilters(facet.facets)
            setFilteredIds([...facet.directHits.map((hit: any) => (hit.id))])

            const newProjects = facet.facets.filter(filter => filter.facetKey === 'relApplicationToProject').length > 0
              ? facet.facets.filter(filter => filter.facetKey === 'relApplicationToProject')[0].keys
              : []

            const newUsers = facet.facets.filter(filter => filter.facetKey === 'Subscriptions').length > 0
              ? facet.facets.filter(filter => filter.facetKey === 'Subscriptions')[0].keys
              : []

            // Check if the new values are different from the current ones before updating
            if (
              JSON.stringify(newProjects) !== JSON.stringify(customFilters.selectedProjects)
              || JSON.stringify(newUsers) !== JSON.stringify(customFilters.userIds)
            ) {
              setCustomFilters({
                selectedProjects: newProjects,
                userIds: newUsers
              })
            }
          }
        }
      ],
      ui: {
        elements: {
          root: {
            items: [
              {
                id: 'toggleLabels',
                type: 'button',
                label: {
                  text: 'Toggle labels'
                },
                click: toggleLabels
              },
              {
                id: 'sortDropdown',
                type: 'dropdown',
                entries: [
                  {
                    id: 'alphabetical',
                    label: 'A ... Z'
                  },
                  {
                    id: 'startDate',
                    label: 'Start date'
                  },
                  {
                    id: 'endDate',
                    label: 'End date'
                  }
                ],
                label: 'Sort'
              },
              {
                id: 'zoomYearWidth',
                type: 'zoom'
              }

            ],
            style: { justifyContent: 'end' } // right-align the element above
          },
          values: {
            sortDropdown: customState.sortOrder,
            zoomYearWidth: customState.zoomLevel
          }
        },
        update: (selection: lxr.UISelection) => {
          console.log('Run selection')
          // #save-state
          console.log('test', lx.latestPublishedState)
          console.log('saved state', setupInfo.savedState)
          // If user change sort order or zoom level -> update them
          console.log('selection.elements', selection.elements)
          if (selection.elements) {
            // True if the report was already initialized (lx.latestPublishedState is null the first time this function runs)
            const updatedState: CustomState = {
              ...(lx.latestPublishedState ?? customState),
              sortOrder: selection.elements.values.sortDropdown as string,
              zoomLevel: selection.elements.values.zoomYearWidth as number
            }
            console.log('updated state in ui update: ', updatedState)
            setSortOrder(selection.elements.values.sortDropdown as string)
            setZoomLevel(selection.elements.values.zoomYearWidth as number)
            lx.publishState(updatedState)
          }
          return undefined
        }
      }
    }
  }

  useEffect(() => {
    console.log('reportSettingsValues was updated: ', reportSettingsValues)
  }, [reportSettingsValues])

  async function openSettingsForm() {
    lx.openFormModal(reportSettingsFields.current, lx.latestPublishedState.currentSettings).then((currentSettings) => {
      if (currentSettings) {
        setReportSettingsValues(currentSettings)

        const currentPublishedState = lx.latestPublishedState ?? getEmptyState()

        const updatedState: CustomState = {
          ...currentPublishedState,
          currentSettings
        }

        console.log('updatedState', updatedState)

        lx.publishState(updatedState)
      }
    })
  }

  useEffect(() => {
    const updated = getReportConfig(lx.currentSetup)
    const facet = updated && updated.facets && updated.facets?.length > 0 ? updated.facets[0] : undefined

    if (facet) {
      const projectFilter: FacetFilter = {
        facetKey: 'relApplicationToProject',
        keys: customFilters.selectedProjects,
        operator: 'OR' as FacetKeyOperator
      }

      const userFilter: FacetFilter = {
        facetKey: 'Subscriptions',
        operator: 'OR' as any,
        keys: [
          ...customFilters.userIds
        ],
        subscriptionFilter: {
          type: 'RESPONSIBLE',
          roleId: 'd2096746-2a62-4cd9-b6a2-307bb4b5a2a5'
        }
      }

      const newFacet: ReportFacetsConfig = {
        ...facet,
        defaultFilters: facet.defaultFilters ? [...facet.defaultFilters, projectFilter, userFilter] : [projectFilter, userFilter]
      }

      if (JSON.stringify(facet.defaultFilters) !== JSON.stringify(newFacet.defaultFilters)) {
        lx.updateConfiguration({
          ...updated,
          facets: []
        })

        lx.updateConfiguration({
          ...updated,
          facets: [newFacet]

        })
      }
    }
  }, [customFilters])

  // useEffect(() => {
  //  console.log('update report config')
  //  if (reportConfig.hasOwnProperty('ui')) {
  //    lx.updateConfiguration(reportConfig)
  //  }
  // }, [reportConfig])

  // async function createReportConfiguration() {

  const initializeReport = async () => {
    const setupInfo = await lx.init()

    if (setupInfo.settings.currentUser) {
      setCurrentUser(setupInfo.settings.currentUser)
    }

    factsheetsMetaData = setupInfo.settings.viewModel.factSheets
    // #save-state
    console.log('saved state inside init', setupInfo.savedState)
    console.log(setupInfo.savedState)

    // Initialize tags and tags colors from setup
    const [allTagsResponse, tagColorsResponse] = await loadTags()

    setAllTags(allTagsResponse)
    setTagColors(tagColorsResponse)

    // Load configuration data from the workspace
    reportSettingsFields.current = await configureSettingsFields(setupInfo.settings.dataModel.factSheets.Application.relations)

    // Get relationships and their FS type for drillDown
    const appRelationsDict = getAppRelationsDict(setupInfo.settings.dataModel)

    const appRelationsResponse = await fetchAppRelations(appRelationsDict)

    setAppRelations(appRelationsResponse)

    const config: lxr.ReportConfiguration = getReportConfig(setupInfo)

    // setReportConfig(config)
    lx.ready(config)
  }

  useEffect(() => {
    initializeReport().then()
  }, [])

  useEffect(() => {
    const excludedClasses = ['app-name-wrap', 'drill-down-wrapper']

    const handleClick = (event: any) => {
      const isExcluded = excludedClasses.some(className =>
        event.target.closest(`.${className}`) !== null
      )

      const clickedWindow = event.target.ownerDocument.defaultView

      if (!isExcluded && clickedWindow === window) {
        setSidePaneApp('')
      }
    }

    document.body.addEventListener('click', handleClick)

    return () => {
      document.body.removeEventListener('click', handleClick)
    }
  }, [])

  useEffect(() => {
    if ((apps as AppFactSheet[]).length > 0 || ('No Cluster' in apps)) {
      setLoading(false)
    }
  }, [apps])

  if (loading) {
    return <Skeleton active />
  }
  if (reportSettingsValues.clusterBy && (apps as AppFactSheet[]).length > 0 && !('No Cluster' in apps)) {
    return <></>
  }
  if (reportSettingsValues.clusterBy === null && ('No Cluster' in apps)) {
    return <></>
  }
  /* if ((apps as AppFactSheet[]).length < 1) return <Alert style={{width: "460px", marginTop: "30px"}} message="Warning"
                                                           type="warning"
                                                           description="There are no applications that match the specified filters."
                                                           showIcon closable/> */
  if (!tagColors) {
    return <></>
  }

  return (
    <appsDrillDownsContext.Provider value={appRelations}>
      <>
        <div style={{ position: 'fixed', zIndex: 1000, margin: '4px', width: '168px' }}>
          <Search
            value={searchText}
            onInput={event => setSearchText((event.target as HTMLInputElement).value)}
            placeholder="Search Applications..."
            size="small"
          />
        </div>
        <div style={{ position: 'fixed', zIndex: 1000, margin: '4px', marginLeft: '178px', marginTop: '6px' }}>
          <CustomFilter
            displayMode={displayMode}
            setDisplayMode={setDisplayMode}
            currentUser={currentUser}
            customFilters={customFilters}
            setCustomFilters={setCustomFilters}
          />
        </div>

        <h1 style={{ position: 'fixed', top: '0px', zIndex: '10000000', left: '0px' }}>
        </h1>

        {((apps as AppFactSheet[]).length < 1) && (
          <Alert
            style={{
              width: '460px',
              left: 0,
              right: 0,
              margin: 'auto',
              marginTop: '100px',
              position: 'absolute'
            }}
            message="Warning"
            type="warning"
            description="There are no applications that match the specified filters."
            showIcon
            closable
          />
        )}

        <Roadmap
          updateApplication={updateApplication}
          refresh={refreshApplications}
          apps={apps}
          years={years}
          showLabels={showLabels}
          yearWidth={yearWidth}
          timelineDates={timelineDates}
          todayMarker={todayMarker}
          zoomLevel={zoomLevel}
          clusterBy={reportSettingsValues.clusterBy as string}
          drillDownBy={reportSettingsValues.drillDown as string}
          allTags={allTags}
          tagColors={tagColors}
          setSidePaneApp={setSidePaneApp}
          sidePaneApp={sidePaneApp}
        />
      </>
    </appsDrillDownsContext.Provider>
  )
}
