import React, { useEffect, useState } from "react";
import { Col, Row, Tabs, TabsProps } from "antd";
import "./App.scss";
import "@leanix/reporting";
import { FACTSHEET_QUERY_FIELDS } from "./utils";
import { parseRawApplicationData } from "./utils/parser";
import { ApplicationModel } from "./utils/models";
import DataTable from "./components/Table";
import { RATING2 } from "./utils/constants";

function App() {

  const [applications, setApplications] = useState<ApplicationModel[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<ApplicationModel[]>([]);
  const [activeTab, setActiveTab] = useState("overview");

  const configuration:any =  {
    facets: [
      {
        key: "1",
        fixedFactSheetType: "Application",
        attributes: [
          ...FACTSHEET_QUERY_FIELDS,
          "relToChild{edges{node {factSheet {... on Application {"+ FACTSHEET_QUERY_FIELDS.join("\n") +"}}}}}"
        ],
        sortings: [
          {
            "key": "displayName",
            "order": "asc"
          }
        ],
        defaultFilters: [
          {
            facetKey: "_TAGS_",
            keys: ["9878f760-b465-4626-a6a8-7c17db54bff7"]
          }
        ],
        callback(data: any) {
          //console.log("APPLICATION: filter update");

          const parsed  = parseRawApplicationData(data)
          setApplications(parsed);


          console.log("ACTIVE TAB", activeTab)

          if(activeTab === "overview"){
            setFilteredApplications(parsed);
          } else {
            setFilteredApplications(parsed.filter(a=> a.dpDataProtectionRating === activeTab))
          }

        },
      },
    ],
  }


  const initializeReport = async () => {

    await lx.init()
    lx.ready(configuration);
  };

  useEffect(() => {
    initializeReport();
  }, [lx]);


  useEffect(() => {
    lx.updateConfiguration(configuration)
  }, [activeTab])



  const tabChange = (key: string) => {

    console.log("set active tab to" , key)
    setActiveTab(key)
    if(key === "overview"){
      setFilteredApplications(applications)
    } else {
      setFilteredApplications(applications.filter(a=> a.dpDataProtectionRating === key))
    }

  }

  const tabs: TabsProps['items'] = [
    {
      key: 'overview',
      label: `Overview`,
      children: <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.PERSONAL_USER_ACCESS_DATA.label,
      label: RATING2.PERSONAL_USER_ACCESS_DATA.label,
      children:  <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.SENSITIVE_PERSONAL_USER_ACCESS_DATA.label,
      label: RATING2.SENSITIVE_PERSONAL_USER_ACCESS_DATA.label,
      children: <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.PERSONAL_STORED_PROCESSED_DATA.label,
      label: RATING2.PERSONAL_STORED_PROCESSED_DATA.label,
      children: <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.SENSITIVE_PERSONAL_STORED_PROCESSED_DATA.label,
      label: RATING2.SENSITIVE_PERSONAL_STORED_PROCESSED_DATA.label,
      children: <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.NO_PERSONAL_DATA.label,
      label: RATING2.NO_PERSONAL_DATA.label,
      children: <DataTable data={filteredApplications}/>,
    },
    {
      key: RATING2.NOT_RATED.label,
      label: RATING2.NOT_RATED.label,
      children: <DataTable data={filteredApplications}/>,
    },
  ];


  return (
    <>
    <Row>
      <Col span={24}>
      <Tabs destroyInactiveTabPane defaultActiveKey="overview" items={tabs} onChange={tabChange}/>    
    
      </Col>
    </Row>
    </>
  );
}

export default App;
