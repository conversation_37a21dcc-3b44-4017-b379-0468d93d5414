import type { Dashboard } from '@app/model'
import DataQualityOverviewContainer from '@components/DataQualityOverviewContainer'
import { DASHBOARDS } from '@config/settings'
import { useEffect, useState } from 'react'
import './App.scss'
import '@leanix/reporting'
import FacetKeyOperator = lxr.FacetKeyOperator

function App() {
  const [loading, setLoading] = useState(true)
  const [rawData, setRawData] = useState<any[]>([])

  const [defaultDashboard, setDefaultDashboard] = useState<Dashboard | null>(null)

  const queryAttributes = [
    'id',
    'name',
    'level',
    'lifecycle {asString phases {phase startDate}}',
    'description',
    'plattform',
    'sourcing',
    'softwareType',
    'guiType',
    'category',
    'tags {id name color tagGroup {id}}',
    'subscriptions {edges {node {user { email} roles {name id}}}}',
    'relApplicationToDomain {edges {node {factSheet {displayName}}}}',
    'relApplicationToBusinessCapability {edges {node {factSheet {displayName}}}}',
    'relApplicationToUserGroup {edges { node { factSheet { displayName} usageType}}}',
    'relApplicationToITComponent { edges { node { factSheet { name id}}}}',
    'hostingProviderCountry',
    'UserAccessDataCategory',
    'relApplicationToHostingProvider{edges{node{factSheet{id name}}}}',
    'relApplicationToOperationsProvider{edges{node{factSheet{id name}}}}',
    'relApplicationToSoftwareServiceProvider{edges{node{factSheet{id name}}}}'
  ]

  const initializeReport = async () => {
    if (document.referrer) {
      const urlParams = new URLSearchParams(document.referrer.split('?')[1])
      const useCase = urlParams.get('dashboard')
      if (useCase && useCase.length > 0) {
        const dashboard = DASHBOARDS.find(d => d.id === useCase)
        if (dashboard) {
          setDefaultDashboard(dashboard)
        }
      }
    }

    await lx.init()
    lx.ready({
      facets: [
        {
          key: '1',
          fixedFactSheetType: 'Application',
          attributes: [
            ...queryAttributes,
            `relToChild{edges {node {factSheet {... on Application {${
              queryAttributes.join('\n')
            }relToChild{edges {node {factSheet {... on Application {id name}}}}}`
            + `}}}}}`
          ],
          defaultFilters: [
            {
              facetKey: 'lifecycle',
              operator: 'OR' as FacetKeyOperator,
              keys: [
                '__missing__',
                'active',
                'phaseIn',
                'phaseOut',
                'plan'
              ],
              dateFilter: {
                type: 'TODAY'
              }
            },
            {
              facetKey: 'hierarchyLevel',
              operator: 'OR' as FacetKeyOperator,
              keys: [
                '1'
              ]
            }
            /*  {
                            "facetKey": "sourcing",
                            "operator": "OR" as FacetKeyOperator,
                            "keys": [
                                "iaas"
                            ]
                        }, */
          ],
          sortings: [{
            mode: 'BY_FIELD',
            key: 'name',
            order: 'desc'
          }],
          callback(data: any) {
            setRawData(data)
            setLoading(false)
          },
          facetFiltersChangedCallback(data: any) {
            console.log(data)
            setLoading(true)
          }
        }
      ]
    })
  }

  useEffect(() => {
    initializeReport().then()
  }, [])

  return (<DataQualityOverviewContainer rawData={rawData} loading={loading} defaultDashboard={defaultDashboard} />)
}

export default App
