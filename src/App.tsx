import type { TabsProps } from 'antd/es/tabs'
import type { ApplicationModel, LeanIXData } from './utils/parser'
import { Col, Row, Tabs } from 'antd'
import { useEffect, useState } from 'react'
import DataTable from './components/Table'
import { BusinessCriticalities, OR } from './utils/constants'
import { parseRawApplicationData } from './utils/parser'
import './App.scss'
import '@leanix/reporting'

function App() {
  const [applications, setApplications] = useState<ApplicationModel[]>([])
  const [filteredApplications, setFilteredApplications] = useState<ApplicationModel[]>([])
  const [activeTab, setActiveTab] = useState<string>('overview')
  const [searchText, setSearchText] = useState<string>('')
  const [ressortFilterValue, setRessortFilterValue] = useState<string>('all')
  const [showAll, setShowAll] = useState<boolean>(true)

    const filterByTab = (key: string) => {
        if (key === 'overview') {
            setFilteredApplications(applications)
        }
        else if (key === '-') {
            setFilteredApplications(
                applications.filter(
                    (a: ApplicationModel) => a.businessCriticality === ''
                )
            )
        }
        else {
            setFilteredApplications(
                applications.filter(
                    (a: ApplicationModel) => a.businessCriticality === key
                )
            )
        }
    }


    const tabChange = (key: string) => {
    // console.log("set active tab to", key);
    setActiveTab(key)
    filterByTab(key)
  }


  const initializeReport = async () => {
    const queryAttributes = [
      'id',
      'name',
      'level',
      'lifecycle {asString phases {phase startDate}}',
      'description',
      'plattform',
      'sourcing',
      'softwareType',
      'businessCriticality',
      'businessCriticalityDescription',
      'tags {id name color tagGroup {id}}',
      'relApplicationToDomain {edges {node {factSheet {displayName}}}}'
    ]

    await lx.init()

    lx.ready({
      facets: [
        {
          key: '1',
          fixedFactSheetType: 'Application',
          attributes: [
            ...queryAttributes,
            `relToChild{edges {node {factSheet {... on Application {${
              queryAttributes.join('\n')
            }}}}}}`
          ],
          defaultFilters: [
            {
              facetKey: 'hierarchyLevel',
              keys: ['1']
            },
            {
              facetKey: 'lifecycle',
              keys: ['plan', 'phaseIn', 'active', 'phaseOut', '__missing__'],
              operator: OR,
              dateFilter: {
                type: 'TODAY'
              }
            },
            {
              facetKey: 'relApplicationToUserGroup',
              operator: 'OR' as any,
              keys: [
                '71d7e010-921a-41a0-aaca-01dc693316f5',
                'e8ec9a5c-a967-44f8-9fb8-0fd46830363c'
              ]
            }
            /* {
                          facetKey: "sourcing",
                          keys: ["saas"],
                        }, */
          ],
          callback(data: LeanIXData[]) {
            // console.log("CALLBACK");
            // console.log("APPLICATION: filter update", data.length);
            const parsed = parseRawApplicationData(data)
            // console.log("count parsed", parsed.length);

            // zurückgegeben levels
            const uniqueLevels = [...new Set(parsed.map(app => app.level))]
            // console.log("levels;", uniqueLevels)

            if (uniqueLevels.includes(1)) {
              // remove dupplicated level2/3 items
              const filtered = parsed.filter(a => a.level === 1)
              setApplications(filtered)
              setFilteredApplications(filtered)
            }
            else {
              setApplications(parsed)
              setFilteredApplications(parsed)
            }

            // setApplications(parsed);
            // setFilteredApplications(parsed);
          }
        }
      ]
    })
  }

  useEffect(() => {
    // console.log("USE EFFECT CALLED");
    initializeReport()
  }, [])

  useEffect(() => {
    filterByTab(activeTab)
  }, [filteredApplications])

  const tabs: TabsProps['items'] = [
    {
      key: 'overview',
      label: `Overview`,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    },
    {
      key: BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.value,
      label: BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.label,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    },
    {
      key: BusinessCriticalities.MISSION_CRITICAL.value,
      label: BusinessCriticalities.MISSION_CRITICAL.label,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    },
    {
      key: BusinessCriticalities.BUSINESS_CRITICAL.value,
      label: BusinessCriticalities.BUSINESS_CRITICAL.label,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    },
    {
      key: BusinessCriticalities.BUSINESS_OPERATIONAL.value,
      label: BusinessCriticalities.BUSINESS_OPERATIONAL.label,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    },
    {
      key: BusinessCriticalities.NO_CRITICALITY.value,
      label: BusinessCriticalities.NO_CRITICALITY.label,
      children: (
        <DataTable
          data={filteredApplications}
          setSearchText={setSearchText}
          searchText={searchText}
          ressortFilterValue={ressortFilterValue}
          setRessortFilterValue={setRessortFilterValue}
          showAll={showAll}
          setShowAll={setShowAll}
        />
      )
    }
  ]

  return (
    <>
      <Row>
        <Col span={24}>
          <Tabs
            destroyInactiveTabPane
            defaultActiveKey="overview"
            items={tabs}
            onChange={tabChange}
          />
        </Col>
      </Row>
    </>
  )
}

export default App
