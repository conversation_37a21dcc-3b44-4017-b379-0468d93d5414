import React, { useEffect, useState } from "react";
import {Col, Flex, Row, Space, Typography} from "antd";
import "./App.scss";
import "@leanix/reporting";
import ViewModeSelector from "./components/ViewModeSelector";
import {Application, CorpUnit, ViewMode} from "@app/models";
import GroupApplicationsContainer from "./components/groupApplications/GroupApplicationsContainer";
import MarketUnitOverviewContainer from "./components/marketUnitOverview/MarketUnitOverviewContainer";
import {parseApplications} from "./parser";
import MarktUnitOverviewFilter from "./components/marketUnitOverview/MarktUnitOverviewFilter";

function App() {

  const [viewMode, setViewMode] = useState<ViewMode>("groupApplications");
  const [loading,setLoading] = useState<boolean>(true);
  const [applications,setApplications] = useState<Application[]>([]);

  const [corpUnit, setCorpUnit] = useState<CorpUnit>("AT");


  const queryAttributes =  [
      "id",
    "name",
    "level",
    "lifecycle {asString phases {phase startDate}}",
    "description",
      "plattform",
    "serviceNowId {externalId}",
    "ServiceBaselineComment",
    "tags {id name tagGroup {name id}}",
    "relApplicationToProject {edges {node {factSheet {name id}}}}",
    "relApplicationToITComponent {edges {node {factSheet {name id}}}}",
    "relApplicationToDomain {edges {node {factSheet {id displayName}}}}",
    "relApplicationToUserGroup {edges {node {factSheet {... on UserGroup {id name level relToParent {edges {node {factSheet {id name level}}}}}}}}}"
  ];

  const initializeReport = async () => {

    await lx.init()
    lx.ready({
      facets: [
        {
          key: "1",
          fixedFactSheetType: "Application",
          attributes: [
            ...queryAttributes,
            "relToChild{edges {node {factSheet {... on Application {" +
            queryAttributes.join("\n") +
            "}}}}}",
          ],
          defaultFilters: [
            {
              facetKey: "hierarchyLevel",
              keys: ["1"],
            },
            {
              "facetKey": "lifecycle",
              "operator": "OR" as any,
              "keys": [
                "__missing__",
                "active",
                "phaseIn",
                "phaseOut",
                "plan"
              ],
              "dateFilter": {
                "type": "TODAY",
              }
            }
          ],
          facetFiltersChangedCallback() {
            setLoading(true);
          },
          callback(data: any) {
            console.log("APPLICATION: filter update", data);
            //setApplications(data);
            setApplications(parseApplications(data));
            setLoading(false)
          },
        },
      ],
    });
  };

  useEffect(() => {
    initializeReport().then();
  }, []);


  const { Title } = Typography;

  return (
    <>
      <Flex
          justify="space-between"
          align="center"
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: '16px'
          }}
          className="responsive-flex"
      >
        <Title level={3} style={{ margin: 0 }}>Group Applications</Title>

        {viewMode === "marketUnitOverview" && (
            <div style={{ flexGrow: 1, minWidth: '200px' }}>
              <MarktUnitOverviewFilter corpUnit={corpUnit} setCorpUnits={setCorpUnit} />
            </div>
        )}

        <Space style={{ minWidth: 'fit-content' }}>
          <ViewModeSelector viewMode={viewMode} setViewMode={setViewMode} />
        </Space>
      </Flex>

      <Row>
        <Col span={24}>
          {viewMode === "groupApplications" ? (<GroupApplicationsContainer loading={loading} data={applications}/>) :
              (<MarketUnitOverviewContainer data={applications}
                                            corpUnit={corpUnit}
                                            loading={loading}
                                            setCorpUnits={setCorpUnit}/>)}
        </Col>
      </Row>
    </>
  );
}

export default App;
