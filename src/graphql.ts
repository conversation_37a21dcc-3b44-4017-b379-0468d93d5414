export const CREATE_FACTSHEET_MUTATION = `
            mutation ($input: BaseFactSheetInput!, $patches: [Patch]!) {
                createFactSheet(input: $input, patches: $patches) {
                  factSheet {
                    id
                    name
                    description
                    type
                  }
                }
              }
`

export const CREATE_RESOURCE_MUTATION = `mutation ($factSheetId: ID!, $factSheetRev: Long, $name: String!, $description: String, $url: String, $origin: String, $documentType: String, $metadata: String, $refId: String) {
  result: createDocument(factSheetId: $factSheetId, factSheetRev: $factSheetRev, name: $name, description: $description, url: $url, origin: $origin, documentType: $documentType, metadata: $metadata, refId: $refId) {
    id
    name
    description
    url
    createdAt
    fileInformation {
      fileName
      size
      mediaType
      previewImage
      content
    }
    origin
    documentType
    metadata
    refId
  }
}
`

export const ADD_CONSUMER_MUTATION = `
mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "$interfaceId", patches: $patches, validateOnly: false) {
    factSheet {
        id
      rev
      completion {
        percentage
        sectionCompletions {
          name
          percentage
          subSectionCompletions {
            name
            percentage
          }
        }
      }
    }
    newRelations {
      relation {
        ... on RelInterfaceToConsumerApplication {
          id
          activeFrom
          activeUntil
          description
          factSheet {
            id
            displayName
            fullName
            description
            type
            category
            permissions {
              self
              create
              read
              update
              delete
            }
            subscriptions {
              edges {
                node {
                  id
                  type
                  user {
                    id
                    displayName
                    technicalUser
                    email
                  }
                }
              }
            }
            ... on Application {
              ApplicationLifecycle: lifecycle {
                asString
                phases {
                  phase
                  startDate
                }
              }
            }
            tags {
              id
              name
              description
              color
              tagGroup {
                id
                shortName
                name
              }
            }
          }
        }
      }
    }
  }
}

`
