import type { ApplicationModel } from './models'
import { DataSubjectCategories, PersonalDataAttributes, RATING2, RecipientCategories, SensitivePersonalDataAttributes, UserGroupAccessAttributes, UserGroupCategories } from './constants'

const getCorpUnit = (name: string): string => {
  const a1 = name.match('^([^\x20]{2,3})\x20?-\x20?(.+)$')
  if (a1 !== null) {
    return a1[1]
  }
  else {
    return ''
  }
}

const parseAttribute = (attribute: any) => {
  if (attribute === null) {
    return ' '
  }
  else {
    return attribute
  }
}

const parsePlattform = (attribute: any) => {
  if (attribute === null) {
    return ' '
  }
  else {
    return attribute
  }
}

const parseITRessort = (temp: ApplicationModel, tags: any[]) => {
  for (const tag of tags) {
    if (tag.tagGroup !== null) {
      if (tag.tagGroup.id === '6cc13132-a7f7-498f-a3ef-eb1d4c67a615') {
        temp.ITRessort = { name: tag.name, color: tag.color }
      }
    }
  }
}

const parseUserGroupCategories = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case UserGroupCategories.CUSTOMERS.value:
          return UserGroupCategories.CUSTOMERS.label
        case UserGroupCategories.EXTERNAL_CONTRACTORS.value:
          return UserGroupCategories.EXTERNAL_CONTRACTORS.label
        case UserGroupCategories.INTERNAL_HV_EMPLOYEES.value:
          return UserGroupCategories.INTERNAL_HV_EMPLOYEES.label
        case UserGroupCategories.SALES_BUSINESS_PARTNER.value:
          return UserGroupCategories.SALES_BUSINESS_PARTNER.label
        case UserGroupCategories.NO_USER_GROUP_CATEGORIES.value:
          return UserGroupCategories.NO_USER_GROUP_CATEGORIES.label
        default:
          return ''
      }
    })
  }
}

const parseLifecycle = (lifecycle: any) => {
  if (lifecycle !== null) {
    return lifecycle.asString
  }
  else {
    return 'no lifecycle'
  }
}

const parseDomain = (domain: any[]) => {
  if (domain[0] === undefined) {
    return ''
  }
  else {
    return domain[0].node.factSheet.displayName
  }
}

const parseArrayAttribute = (array: any) => {
  let tempString = ''
  if (array.length === 0) {
    return tempString
  }
  else {
    array.forEach((element: any) => {
      tempString += `${element.node.factSheet.name}; `
    })
    return tempString
  }
}

const parseApplicationDomain = (relApplicationToDomain: any) => {
  const domains: string[] = []

  relApplicationToDomain.edges.map((d: any) => domains.push(d.node.factSheet.displayName))

  return domains
}

const parseProcesses = (relApplicationToProcess: any) => {
  const processes: string[] = []

  relApplicationToProcess.edges.map((p: any) => processes.push(p.node.factSheet.displayName))

  return processes
}

const parseBusinessAreas = (relApplicationToUserGroup: any) => {
  const businessAreas: string[] = []

  relApplicationToUserGroup.edges.map((b: any) => businessAreas.push(b.node.factSheet.displayName))

  return businessAreas
}

const parseBusinessCapabilities = (relApplicationToBusinessCapability: any) => {
  const businessCapabilities: string[] = []

  relApplicationToBusinessCapability.edges.map((b: any) => businessCapabilities.push(b.node.factSheet.name))

  return businessCapabilities
}

const parseUserGroupAccessAttributes = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case UserGroupAccessAttributes.LOGIN_CREDENTIALS.value:
          return UserGroupAccessAttributes.LOGIN_CREDENTIALS.label
        case UserGroupAccessAttributes.ACCESS_LOGFILE.value:
          return UserGroupAccessAttributes.ACCESS_LOGFILE.label
        case UserGroupAccessAttributes.ACTIVITY_LOGFILE.value:
          return UserGroupAccessAttributes.ACTIVITY_LOGFILE.label
        case UserGroupAccessAttributes.BIOMETRIC_ACCESS_DATA.value:
          return UserGroupAccessAttributes.BIOMETRIC_ACCESS_DATA.label
        case UserGroupAccessAttributes.NO_USER_GROUP_ATTRIBUTES.value:
          return UserGroupAccessAttributes.NO_USER_GROUP_ATTRIBUTES.label
        default:
          return ''
      }
    })
  }
}

const getRelevance = (app: any) => {
  let highestRating = ''

  let noData1 = false
  let noData2 = false
  let noData3 = false
  let noData1_
  let noData2_

  if (app !== null) {
    // console.log("äpp",app)
    if (app.UserGroupAccessAttributes !== null) {
      if (app.UserGroupAccessAttributes.length > 0) {
        if (app.UserGroupAccessAttributes[0] !== 'noUserGroupAttributes') {
          highestRating = RATING2.PERSONAL_USER_ACCESS_DATA.label // NON_PERSONAL_DATA zu PERSONAL_USER_ACCESS_DATA
        }
        else {
          noData1 = true
        }
        // sonderregelung biometrical acess data
        if (app.UserGroupAccessAttributes.filter((d: any) => d === UserGroupAccessAttributes.BIOMETRIC_ACCESS_DATA.value).length > 0) {
          // console.log("HIGHERST RATING TO " + app.name)
          highestRating = RATING2.SENSITIVE_PERSONAL_USER_ACCESS_DATA.label
        }
      }
    }
    // doppelcheck
    if (highestRating !== RATING2.PERSONAL_USER_ACCESS_DATA.label && highestRating !== RATING2.SENSITIVE_PERSONAL_USER_ACCESS_DATA.label) {
      if (app.UserAccessDataCategory !== null) {
        if (app.UserAccessDataCategory.length > 0) {
          if (app.UserAccessDataCategory[0] !== 'noUserGroupCategories') {
            highestRating = RATING2.PERSONAL_USER_ACCESS_DATA.label // NON_PERSONAL_DATA zu PERSONAL_USER_ACCESS_DATA
          }
          else {
            noData1_ = true
          }
        }
      }
    }
    if (app.PersonalData !== null) {
      if (app.PersonalData.length > 0) {
        if (app.PersonalData[0] !== 'noPersonalDataAttributes') {
          highestRating = RATING2.PERSONAL_STORED_PROCESSED_DATA.label // PERSONAL_DATA zu PERSONAL_STORED_PROCESSED_DATA
        }
        else {
          noData2 = true
        }
      }
    }

    // Doppelcheck
    if (highestRating !== RATING2.PERSONAL_STORED_PROCESSED_DATA.label) {
      if (app.DataSubjectCategory !== null) {
        if (app.DataSubjectCategory.length > 0) {
          if (app.DataSubjectCategory[0] !== 'noDataSubjectCategories') {
            highestRating = RATING2.PERSONAL_STORED_PROCESSED_DATA.label // PERSONAL_DATA zu PERSONAL_STORED_PROCESSED_DATA
          }
          else {
            noData2_ = true
          }
        }
      }
    }

    if (app.SensitivePersonalData !== null) {
      // console.log("SENSITIVE DATA FOUND!!!!-1", app.SensitivePersonalData)
      if (app.SensitivePersonalData.length > 0) {
        // console.log("SENSITIVE DATA FOUND!!!!")
        if (app.SensitivePersonalData[0] !== 'notApplicable') {
          highestRating = RATING2.SENSITIVE_PERSONAL_STORED_PROCESSED_DATA.label // SENSITIVE zu SENSITIVE_PERSONAL_STORED_PROCESSED_DATA
        }
        else {
          noData3 = true
        }
      }
    }
  }

  if (app.UserGroupAccessAttributes == null && app.PersonalData == null && app.SensitivePersonalData == null && app.UserAccessDataCategory == null && app.DataSubjectCategory == null) {
    highestRating = RATING2.NOT_RATED.label
  }

  if (noData1 && noData2 && noData3 && noData1_ && noData2_) {
    highestRating = RATING2.NO_PERSONAL_DATA.label
  }

  return highestRating
}

const parseDataSubjectCategories = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case DataSubjectCategories.CUSTOMER.value:
          return DataSubjectCategories.CUSTOMER.label
        case DataSubjectCategories.PROSPECT.value:
          return DataSubjectCategories.PROSPECT.label
        case DataSubjectCategories.NEWSLETTER_SUBSCRIBER.value:
          return DataSubjectCategories.NEWSLETTER_SUBSCRIBER.label
        case DataSubjectCategories.BENEFICIARY.value:
          return DataSubjectCategories.BENEFICIARY.label
        case DataSubjectCategories.OTHER_INSURANCE_PARTY.value:
          return DataSubjectCategories.OTHER_INSURANCE_PARTY.label
        case DataSubjectCategories.RELATIVES_OF_CUSTOMER.value:
          return DataSubjectCategories.RELATIVES_OF_CUSTOMER.label
        case DataSubjectCategories.SUPPLIER.value:
          return DataSubjectCategories.SUPPLIER.label
        case DataSubjectCategories.CONTACT_PERSON_OF_SUPPLIER.value:
          return DataSubjectCategories.CONTACT_PERSON_OF_SUPPLIER.label
        case DataSubjectCategories.INTERNAL_EMPLOYEE.value:
          return DataSubjectCategories.INTERNAL_EMPLOYEE.label
        case DataSubjectCategories.EXTERNAL_CONTRACTORS.value:
          return DataSubjectCategories.EXTERNAL_CONTRACTORS.label
        case DataSubjectCategories.JOB_APPLICANT.value:
          return DataSubjectCategories.JOB_APPLICANT.label
        case DataSubjectCategories.OTHER_DATA_SUBJECT_CATEGORIES.value:
          return DataSubjectCategories.OTHER_DATA_SUBJECT_CATEGORIES.label
        case DataSubjectCategories.NO_DATA_SUBJECT_CATEGORIES.value:
          return DataSubjectCategories.NO_DATA_SUBJECT_CATEGORIES.label
        default:
          return ''
      }
    })
  }
}

const parsePersonalDataAttributes = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case PersonalDataAttributes.BASIC_DATA.value:
          return PersonalDataAttributes.BASIC_DATA.label
        case PersonalDataAttributes.CONTACT_INFORMATION.value:
          return PersonalDataAttributes.CONTACT_INFORMATION.label
        case PersonalDataAttributes.PERSONAL_IDENTIFIER.value:
          return PersonalDataAttributes.PERSONAL_IDENTIFIER.label
        case PersonalDataAttributes.INSURANCE_DETAILS.value:
          return PersonalDataAttributes.INSURANCE_DETAILS.label
        case PersonalDataAttributes.CLAIM_INFORMATION.value:
          return PersonalDataAttributes.CLAIM_INFORMATION.label
        case PersonalDataAttributes.CONVERSATION_RECORDS.value:
          return PersonalDataAttributes.CONVERSATION_RECORDS.label
        case PersonalDataAttributes.PROFILE_TRACKING_DATA.value:
          return PersonalDataAttributes.PROFILE_TRACKING_DATA.label
        case PersonalDataAttributes.CREDIT_SCORING_INFORMATION.value:
          return PersonalDataAttributes.CREDIT_SCORING_INFORMATION.label
        case PersonalDataAttributes.ANALYTIC_DATA.value:
          return PersonalDataAttributes.ANALYTIC_DATA.label
        case PersonalDataAttributes.FINANCIAL_INFORMATION.value:
          return PersonalDataAttributes.FINANCIAL_INFORMATION.label
        case PersonalDataAttributes.EMPLOYMENT_INFORMATION.value:
          return PersonalDataAttributes.EMPLOYMENT_INFORMATION.label
        case PersonalDataAttributes.FAMILY_RELATIONSHIPS.value:
          return PersonalDataAttributes.FAMILY_RELATIONSHIPS.label
        case PersonalDataAttributes.OTHER_PERSONAL_DATA_ATTRIBUTES.value:
          return PersonalDataAttributes.OTHER_PERSONAL_DATA_ATTRIBUTES.label
        case PersonalDataAttributes.NO_PERSONAL_DATA_ATTRIBUTES.value:
          return PersonalDataAttributes.NO_PERSONAL_DATA_ATTRIBUTES.label
        default:
          return ''
      }
    })
  }
}

const parseSensitivePersonalDataAttributes = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case SensitivePersonalDataAttributes.HEALTH_INFORMATION.value:
          return SensitivePersonalDataAttributes.HEALTH_INFORMATION.label
        case SensitivePersonalDataAttributes.RELIGIOUS_POLITICAL_OPINION
          .value:
          return SensitivePersonalDataAttributes.RELIGIOUS_POLITICAL_OPINION
            .label
        case SensitivePersonalDataAttributes.SEXUAL_ORIENTATION.value:
          return SensitivePersonalDataAttributes.SEXUAL_ORIENTATION.label
        case SensitivePersonalDataAttributes.GENETIC_DATA.value:
          return SensitivePersonalDataAttributes.GENETIC_DATA.label
        case SensitivePersonalDataAttributes.RACIAL_ETHNIC_DATA.value:
          return SensitivePersonalDataAttributes.RACIAL_ETHNIC_DATA.label
        case SensitivePersonalDataAttributes.BIOMETRIC_DATA.value:
          return SensitivePersonalDataAttributes.BIOMETRIC_DATA.label
        case SensitivePersonalDataAttributes.PROCEEDINGS_SANCTIONS.value:
          return SensitivePersonalDataAttributes.PROCEEDINGS_SANCTIONS.label
        case SensitivePersonalDataAttributes.SOCIAL_SECURITY_MEASURES.value:
          return SensitivePersonalDataAttributes.SOCIAL_SECURITY_MEASURES
            .label
        case SensitivePersonalDataAttributes
          .OTHER_SENSITIVE_PERSONAL_DATA_ATTRIBUTES
          .value:
          return SensitivePersonalDataAttributes
            .OTHER_SENSITIVE_PERSONAL_DATA_ATTRIBUTES
            .label
        case SensitivePersonalDataAttributes.NOT_APPLICABLE.value:
          return SensitivePersonalDataAttributes.NOT_APPLICABLE.label

        default:
          return ''
      }
    })
  }
}

const parseRecipientCategories = (input: any) => {
  if (input == null) {
    return []
  }
  else {
    return input.map((i: any) => {
      switch (i) {
        case RecipientCategories.MARKET_UNITS.value:
          return RecipientCategories.MARKET_UNITS.label
        case RecipientCategories.SERVICE_PROVIDERS.value:
          return RecipientCategories.SERVICE_PROVIDERS.label
        case RecipientCategories.INSURANCE_BROKERS.value:
          return RecipientCategories.INSURANCE_BROKERS.label
        case RecipientCategories.OTHER_CONTRACTED_PARTNERS.value:
          return RecipientCategories.OTHER_CONTRACTED_PARTNERS.label
        case RecipientCategories.OTHER_INSURERS.value:
          return RecipientCategories.OTHER_INSURERS.label
        case RecipientCategories.PENSION_INSTITUTIONS.value:
          return RecipientCategories.PENSION_INSTITUTIONS.label
        case RecipientCategories.SOCIAL_INSURERS.value:
          return RecipientCategories.SOCIAL_INSURERS.label
        case RecipientCategories.SUBJECT_MATTER_EXPERTS.value:
          return RecipientCategories.SUBJECT_MATTER_EXPERTS.label
        case RecipientCategories.AUTHORITIES.value:
          return RecipientCategories.AUTHORITIES.label
        case RecipientCategories.ASSOCIATIONS.value:
          return RecipientCategories.ASSOCIATIONS.label
        case RecipientCategories.OTHER_RECIPIENTS.value:
          return RecipientCategories.OTHER_RECIPIENTS.label
        default:
          return ''
      }
    })
  }
}

const parseRelToChild = (relToChild: any) => {
  const tempArray: ApplicationModel[] = []

  for (const appItem of relToChild.edges) {
    const temp: ApplicationModel = {
      id: appItem.node.factSheet.id,
      rowid: appItem.node.factSheet.id,
      name: appItem.node.factSheet.name,

      alias: appItem.node.factSheet.alias,
      grcId: appItem.node.factSheet.grcId,
      businessCriticality: appItem.node.factSheet.businessCriticality,
      businessCriticalityDescription: appItem.node.factSheet.businessCriticalityDescription,
      finmaRelevancy: appItem.node.factSheet.finmaRelevancy,
      CloudTarget: appItem.node.factSheet.CloudTarget,
      CloudTargetDate: appItem.node.factSheet.CloudTargetDate,
      CloudTransitionComment: appItem.node.factSheet.CloudTransitionComment,
      saaSClassification: appItem.node.factSheet.saaSClassification,
      saaSClassificationComment: appItem.node.factSheet.saaSClassificationComment,
      infoSecConfidentiality: appItem.node.factSheet.infoSecConfidentiality,
      infoSecIntegrity: appItem.node.factSheet.infoSecIntegrity,
      infoSecAvailability: appItem.node.factSheet.infoSecAvailability,
      userAdmin: appItem.node.factSheet.userAdmin,
      successors: [...appItem.node.factSheet.relToSuccessor.edges.map((s: any) => s.node.factSheet.displayName)],
      lifecycleRaw: appItem.node.factSheet.lifecycle,
      status: appItem.node.factSheet.status,
      applicationType: appItem.node.factSheet.applicationType,

      corpUnit: getCorpUnit(appItem.node.factSheet.name),
      ITRessort: { name: 'not assigned', color: '#FFFFFF' },
      level: appItem.node.factSheet.level,
      lifeCycle: parseLifecycle(appItem.node.factSheet.lifecycle),
      description: parseAttribute(appItem.node.factSheet.description),
      domain: parseDomain(
        appItem.node.factSheet.relApplicationToDomain.edges
      ),
      plattform: parsePlattform(appItem.node.factSheet.plattform),
      hosting: parseAttribute(appItem.node.factSheet.sourcing),
      thirdParty: parseAttribute(appItem.node.factSheet.softwareType),
      applications: undefined,
      businessCapability: parseArrayAttribute(
        appItem.node.factSheet.relApplicationToBusinessCapability.edges
      ),
      relevancy: appItem.node.factSheet.gdprRating,
      purposeOfProcessing: appItem.node.factSheet.gdprPurposeOfProcessing,

      privacyAttributeMissing: false,
      confidentiality: appItem.node.factSheet.infoSecConfidentiality,
      integrity: appItem.node.factSheet.infoSecIntegrity,
      availabilty: appItem.node.factSheet.infoSecAvailability,

      applicationDomains: parseApplicationDomain(appItem.node.factSheet.relApplicationToDomain),
      businessCapabilities: parseBusinessCapabilities(appItem.node.factSheet.relApplicationToBusinessCapability),
      processes: parseProcesses(appItem.node.factSheet.relApplicationToProcess),
      businessAreas: parseBusinessAreas(appItem.node.factSheet.relApplicationToUserGroup),

      dpUserAccessDataCategory: parseUserGroupCategories(
        appItem.node.factSheet.UserAccessDataCategory
      ),
      dpUserGroupAccessAttributes: parseUserGroupAccessAttributes(appItem.node.factSheet.UserGroupAccessAttributes),
      dpDataSubjectCategory: parseDataSubjectCategories(appItem.node.factSheet.DataSubjectCategory),
      dpPersonalData: parsePersonalDataAttributes(appItem.node.factSheet.PersonalData),
      dpSensitivePersonalData: parseSensitivePersonalDataAttributes(appItem.node.factSheet.SensitivePersonalData),
      dpAdditionalCategoriesAttributes:
      appItem.node.factSheet.additionalCategoriesAttributes,
      dpGdprPurposeOfProcessing: appItem.node.factSheet.gdprPurposeOfProcessing,
      dpGdprClientFacing: appItem.node.factSheet.gdprClientFacing,
      dpGdprDataPrivacyComment: appItem.node.factSheet.gdprDataPrivacyComment,
      dpGdprDataPrivacyAgreement: appItem.node.factSheet.gdprDataPrivacyAgreement,
      dpGdprDataPrivacyStatement: appItem.node.factSheet.gdprDataPrivacyStatement,
      dpGdprCrossBorderTransfer: appItem.node.factSheet.gdprCrossBorderTransfer,
      dpGdprCrossBorderComment: appItem.node.factSheet.gdprCrossBorderComment,
      dpExternalSupplier: appItem.node.factSheet.externalSupplier,
      dpRententionPeriods: appItem.node.factSheet.rententionPeriods,
      dpRecipientCategories: parseRecipientCategories(appItem.node.factSheet.recipientCategories),
      dpRecipientComment: appItem.node.factSheet.recipientComment,
      dpDataProtectionRating: getRelevance(appItem.node.factSheet)
    }

    parseITRessort(temp, appItem.node.factSheet.tags)

    if (
      temp.lifeCycle !== 'endOfLife'
      && (temp.corpUnit === 'CF' || temp.corpUnit === 'CH')
    ) {
      tempArray.push(temp)
    }
  }
  return tempArray
}

export const parseRawApplicationData = (jsonInput: any): ApplicationModel[] => {
  const tempArray: ApplicationModel[] = []

  for (const appItem of jsonInput) {
    // TODOD for testing
    /* if(appItem.node.name !== undefined && appItem.node.name.includes("leanix")){
          console.log(appItem.node.name)
            console.log(appItem);
        } */

    const temp: ApplicationModel = {
      id: appItem.id,
      rowid: appItem.id,
      name: appItem.name,
      corpUnit: getCorpUnit(appItem.name),
      ITRessort: { name: 'not assigned', color: '#FFFFFF' },
      level: appItem.level,

      status: appItem.status,
      alias: appItem.alias,
      applicationType: appItem.applicationType,
      grcId: appItem.grcId?.externalId,
      businessCriticality: appItem.businessCriticality,
      businessCriticalityDescription: appItem.businessCriticalityDescription,
      finmaRelevancy: appItem.finmaRelevancy,
      CloudTarget: appItem.CloudTarget,
      CloudTargetDate: appItem.CloudTargetDate,
      CloudTransitionComment: appItem.CloudTransitionComment,
      saaSClassification: appItem.saaSClassification,
      saaSClassificationComment: appItem.saaSClassificationComment,
      infoSecConfidentiality: appItem.infoSecConfidentiality,
      infoSecIntegrity: appItem.infoSecIntegrity,
      infoSecAvailability: appItem.infoSecAvailability,
      userAdmin: appItem.userAdmin,
      successors: [...appItem.relToSuccessor.edges.map((s: any) => s.node.factSheet.displayName)],
      lifecycleRaw: appItem.lifecycle,

      lifeCycle: parseLifecycle(appItem.lifecycle),
      description: parseAttribute(appItem.description),
      domain: parseDomain(
        appItem.relApplicationToDomain.edges
      ),
      plattform: parsePlattform(appItem.plattform),
      hosting: parseAttribute(appItem.sourcing),
      thirdParty: parseAttribute(appItem.softwareType),
      businessCapability: parseArrayAttribute(
        appItem.relApplicationToBusinessCapability.edges
      ),
      applications: [],
      relevancy: appItem.gdprRating,
      purposeOfProcessing: appItem.gdprPurposeOfProcessing,
      privacyAttributeMissing: false,
      confidentiality: appItem.infoSecConfidentiality,
      integrity: appItem.infoSecIntegrity,
      availabilty: appItem.infoSecAvailability,

      applicationDomains: parseApplicationDomain(appItem.relApplicationToDomain),
      businessCapabilities: parseBusinessCapabilities(appItem.relApplicationToBusinessCapability),
      processes: parseProcesses(appItem.relApplicationToProcess),
      businessAreas: parseBusinessAreas(appItem.relApplicationToUserGroup),

      dpUserAccessDataCategory: parseUserGroupCategories(
        appItem.UserAccessDataCategory
      ),
      dpUserGroupAccessAttributes: parseUserGroupAccessAttributes(
        appItem.UserGroupAccessAttributes
      ),
      dpDataSubjectCategory: parseDataSubjectCategories(appItem.DataSubjectCategory),
      dpPersonalData: parsePersonalDataAttributes(appItem.PersonalData),
      dpSensitivePersonalData: parseSensitivePersonalDataAttributes(appItem.SensitivePersonalData),
      dpAdditionalCategoriesAttributes:
            appItem.additionalCategoriesAttributes,
      dpGdprPurposeOfProcessing: appItem.gdprPurposeOfProcessing,
      dpGdprClientFacing: appItem.gdprClientFacing,
      dpGdprDataPrivacyComment: appItem.gdprDataPrivacyComment,
      dpGdprDataPrivacyAgreement: appItem.gdprDataPrivacyAgreement,
      dpGdprDataPrivacyStatement: appItem.gdprDataPrivacyStatement,
      dpGdprCrossBorderTransfer: appItem.gdprCrossBorderTransfer,
      dpGdprCrossBorderComment: appItem.gdprCrossBorderComment,
      dpExternalSupplier: appItem.externalSupplier,
      dpRententionPeriods: appItem.rententionPeriods,
      dpRecipientCategories: parseRecipientCategories(appItem.recipientCategories),
      dpRecipientComment: appItem.recipientComment,
      dpDataProtectionRating: getRelevance(appItem)
    }

    parseITRessort(temp, appItem.tags)

    if (appItem.relToChild.edges.length !== 0) {
      temp.applications = parseRelToChild(appItem.relToChild)
    }
    else {
      temp.applications = undefined
    }
    if (
      temp.lifeCycle !== 'endOfLife'
      && (temp.corpUnit === 'CF' || temp.corpUnit === 'CH')
    ) {
      tempArray.push(temp)
    }
  }
  return tempArray
}
