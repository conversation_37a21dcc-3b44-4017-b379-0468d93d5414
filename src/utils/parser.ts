export interface ApplicationModel {
    id: string,
    rowid: string,
    name: string,
    ITRessort: TagModel,
    corpUnit: string,
    level: number,
    lifeCycle: string,
    description: string,
    domain: string,
    plattform: string,
    hosting: string,
    thirdParty: string,
    applications: ApplicationModel[] | undefined
    businessCriticality: string,
    businessCriticalityDescription: string,
  
  }
  
  export interface TagModel {
    name: string,
    color: string,
  }
  

  export interface LeanIXLifecycle {
    asString:string;
    phases: {
      phase:string;
      startDate:string;
    }[]
  }

export interface LeanIXTag{
    id: string;
    name: string;
    color:string;
    tagGroup: {
      id: string;
    }
}

  export interface LeanIXData {
    id: string;
    name:string;
    description: string;
    businessCriticality: string | null;
    businessCriticalityDescription: string;
    level: number;
    type:string;
    tags: LeanIXTag[];
    sourcing: string;
    softwareType: string;
    plattform: string | null;
    lifecycle: LeanIXLifecycle | null;
    relToChild: {edges:[]};
    relApplicationToDomain: {edges: []};
  }



     export const parseRawApplicationData =(jsonInput: LeanIXData[]): ApplicationModel[] => {

     // console.log(jsonInput)

      let tempArray: ApplicationModel[] = [];
  
      for (let appItem of jsonInput) {
        let temp: ApplicationModel = {
          id: appItem.id,
          rowid: appItem.id,
          name: appItem.name,
          corpUnit: getCorpUnit(appItem.name),
          ITRessort: { name: 'not assigned', color: '#FFFFFF' },
          level: appItem.level,
          lifeCycle: parseLifecycle(appItem.lifecycle), //check if endOfLife or Plan
          description: parseAttribute(appItem.description),
          domain: parseDomain(appItem.relApplicationToDomain.edges),
          plattform: parseAttribute(appItem.plattform),
          hosting: parseAttribute(appItem.sourcing),
          thirdParty: parseAttribute(appItem.softwareType),
          applications: [],
          businessCriticality: parseAttribute(appItem.businessCriticality),
          businessCriticalityDescription: parseAttribute(appItem.businessCriticalityDescription),
          
        }
  
        parseITRessort(temp, appItem.tags)
  
        if (appItem.relToChild.edges.length !== 0) {
          temp.applications = parseRelToChild(appItem.relToChild)
        } else {

          temp.applications = undefined
        }
  
       /* if(temp.name.includes("Meteo")){
          console.log("FOUND", temp)
        }*/


       tempArray.push(temp)
      }
      return tempArray;
    }
  

  
    const getCorpUnit = (name: string): string => {
      let a1 = name.match('^([^\x20]{2,3})\x20?-\x20?(.+)$');
      if (a1 !== null) {
        return a1[1];
      } else {
        return '';
      }
    }
  
    const parseAttribute = (attribute: string | null):string => {

      if (attribute === null) {
        return ''
      } else {
        return attribute
      }
    }
  
  
    const parseITRessort = (temp: ApplicationModel, tags: LeanIXTag[]) => {

      for (let tag of tags) {
        if (tag.tagGroup !== null) {
          tag.tagGroup.id === '6cc13132-a7f7-498f-a3ef-eb1d4c67a615' ? temp.ITRessort = { name: tag.name, color: tag.color } : ''
  
        }
      }
  
    }
  
  
    const parseLifecycle = (lifecycle: LeanIXLifecycle | null) => {
      if (lifecycle !== null) {
        return lifecycle.asString
      } else {
        return "no lifecycle"
      }
    }
  
    const parseDomain = (domain: {node:{factSheet:{displayName:string}}}[]) => {

      if (domain[0] == undefined) {
        return ''
      } else {
        return domain[0].node.factSheet.displayName
      }
    }
  
  
  
    const parseRelToChild = (relToChild: {edges: {node:{factSheet:LeanIXData}}[]}) => {
      let tempArray: ApplicationModel[] = [];
  
      for (let appItem of relToChild.edges) {
  
        let temp: ApplicationModel = {
          id: appItem.node.factSheet.id,
          rowid: appItem.node.factSheet.id,
          name: appItem.node.factSheet.name,
          corpUnit: getCorpUnit(appItem.node.factSheet.name),
          ITRessort: { name: 'not assigned', color: '#FFFFFF' },
          level: appItem.node.factSheet.level,
          lifeCycle: parseLifecycle(appItem.node.factSheet.lifecycle),
          description: parseAttribute(appItem.node.factSheet.description),
          domain: parseDomain(appItem.node.factSheet.relApplicationToDomain.edges),
          plattform: parseAttribute(appItem.node.factSheet.plattform),
          hosting: parseAttribute(appItem.node.factSheet.sourcing),
          thirdParty: parseAttribute(appItem.node.factSheet.softwareType),
          applications: undefined,
          businessCriticality: parseAttribute(appItem.node.factSheet.businessCriticality),
          businessCriticalityDescription: parseAttribute(appItem.node.factSheet.businessCriticalityDescription),
  
        }
  
  
        parseITRessort(temp, appItem.node.factSheet.tags)
  
        /*if (temp.lifeCycle !== 'endOfLife' && (temp.corpUnit === "CF" || temp.corpUnit === "CH")) {*/
          tempArray.push(temp)
        /*}*/
  
      }
      return tempArray;
    }
  
