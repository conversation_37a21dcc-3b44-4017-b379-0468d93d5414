import type {
  ApplicationModel,
  BarChartModel,
  DomainCategoryModel,
  DomainWrapper,
  LoadDataResult,
  PieModel,
  PreviewWrapper
} from '@app/model'
import { CLOUD_JOURNEY_BAR, DOMAIN_CATEGORIES, PIE_COLORS } from '@app/contstants'
import { FILTER_CCC_DASHBOARD_FILTER, QUERY_CCC_DASHBOARD_FILTER, QUERY_SOURCING } from '@utils/queries'
import moment from 'moment'
import * as XLSX from 'xlsx'

const getFirstQuarterOfYear = (year: number) => {
  const date = new Date()
  date.setMonth(2)
  date.setDate(31)
  date.setFullYear(year)

  return date
}

const getCorpUnit = (name: string): string => {
  const a1 = name.match('^([^\x20]{2,3})\x20?-\x20?(.+)$')
  if (a1 !== null) {
    return a1[1]
  }
  else {
    return ''
  }
}

const parseLifeCycle = (lifeCycle: any) => {
  return lifeCycle == null ? '' : lifeCycle.asString
}

const generateDate = (dateString: string) => {
  if (dateString == 'notYetDefined') {
    return new Date(2040, 11, 31)
  }
  else if (dateString != null) {
    return new Date(Number.parseInt(`${dateString.replace('_', '')}`), 11, 31)
  }
  else {
    return new Date(2041, 11, 31)
  }
}

const parseDomain = (domain: any[]) => {
  if (domain[0] == undefined) {
    return null
  }
  else {
    return domain[0].node.factSheet.displayName
  }
}

const parseDomainCategory = (domain: any[]) => {
  if (domain[0] == undefined) {
    return ''
  }
  else {
    return domain[0].node.factSheet.category
  }
}

const parseRawApplicationData = (jsonInput: any): ApplicationModel[] => {
  const tempArray: ApplicationModel[] = []

  for (const app of jsonInput.allFactSheets.edges) {
    const temp: ApplicationModel = {
      id: app.node.id,
      rowid: app.node.id,
      name: app.node.displayName,
      corpUnit: getCorpUnit(app.node.displayName),
      applicationType: app.node.applicationType,
      sourcing: app.node.sourcing,
      lifeCycle: parseLifeCycle(app.node.lifecycle),
      cloudTargetSourcing: app.node.CloudTarget,
      cloudTargetDate: generateDate(app.node.CloudTargetDate),
      domain: parseDomain(app.node.relApplicationToDomain.edges),
      domainTag: parseDomainCategory(app.node.relApplicationToDomain.edges),
      valueToTake: ''
    }

    if (temp.name === 'CF-Bomgar') {
      console.log('JEETZT')
      console.log(app)
      console.log(temp)
    }

    if (temp.domainTag != '') {
      tempArray.push(temp)
    }
  }
  return tempArray
}

const generatePieArray = (values: any[]) => {
  const tempArray: PieModel[] = []
  values.forEach((element) => {
    const temp: PieModel = {
      type: element.name,
      value: 0,
      color: ''
    }
    tempArray.push(temp)
  })

  tempArray.push({ type: 'Rehosting', value: 0, color: '' })
  tempArray.push({ type: 'Replacement', value: 0, color: '' })

  for (let i = 0; i < tempArray.length; i++) {
    tempArray[i].color = PIE_COLORS[i]
  }

  return tempArray
}

export async function getSourcings() {
  return lx.executeGraphQL(QUERY_SOURCING)
    .then((result) => {
      return generatePieArray(result.__type.enumValues)
    })
}

export async function loadData(): Promise<LoadDataResult> {
  const today = moment().format('YYYY-MM-DD')
  const todayDate = new Date()

  let nextQuarterDate, secondYearDate, thirdYearDate

  if (todayDate.getMonth() <= 2 && todayDate.getDate() <= 31) {
    nextQuarterDate = getFirstQuarterOfYear(todayDate.getFullYear())
    secondYearDate = getFirstQuarterOfYear(todayDate.getFullYear() + 1)
    thirdYearDate = getFirstQuarterOfYear(todayDate.getFullYear() + 2)
  }
  else {
    nextQuarterDate = getFirstQuarterOfYear(todayDate.getFullYear() + 1)
    secondYearDate = getFirstQuarterOfYear(todayDate.getFullYear() + 2)
    thirdYearDate = getFirstQuarterOfYear(todayDate.getFullYear() + 3)
  }

  const nextQuarter = moment(nextQuarterDate).format('YYYY-MM-DD')
  const secondYear = moment(secondYearDate).format('YYYY-MM-DD')
  const thirdYear = moment(thirdYearDate).format('YYYY-MM-DD')

  const filterToday = FILTER_CCC_DASHBOARD_FILTER
    .replace('fromDate', today)
    .replace('toDate', today)

  const filterNextQuarter = FILTER_CCC_DASHBOARD_FILTER
    .replace('fromDate', nextQuarter)
    .replace('toDate', nextQuarter)

  const filterSecondYear = FILTER_CCC_DASHBOARD_FILTER
    .replace('fromDate', secondYear)
    .replace('toDate', secondYear)

  const filterThirdYear = FILTER_CCC_DASHBOARD_FILTER
    .replace('fromDate', thirdYear)
    .replace('toDate', thirdYear)

  try {
    const [liveResult, nextYearResult, secondYearResult, thirdYearResult] = await Promise.all([
      lx.executeGraphQL(QUERY_CCC_DASHBOARD_FILTER, filterToday),
      lx.executeGraphQL(QUERY_CCC_DASHBOARD_FILTER, filterNextQuarter),
      lx.executeGraphQL(QUERY_CCC_DASHBOARD_FILTER, filterSecondYear),
      lx.executeGraphQL(QUERY_CCC_DASHBOARD_FILTER, filterThirdYear)
    ])

    return {
      liveData: {
        date: new Date(today),
        data: parseRawApplicationData(liveResult)
      },
      nextYearData: {
        date: new Date(nextQuarter),
        data: parseRawApplicationData(nextYearResult)
      },
      secondYearData: {
        date: new Date(secondYear),
        data: parseRawApplicationData(secondYearResult)
      },
      thirdYearData: {
        date: new Date(thirdYear),
        data: parseRawApplicationData(thirdYearResult)
      }
    }
  }
  catch (error) {
    console.error('Fehler beim Laden der Daten:', error)
    throw error
  }
}

export const calculateData = (data: LoadDataResult) => {
  const todayDate = new Date()
  let nextQuarterDate = null
  let secondYearDate = null
  let thirdYearDate = null

  if (todayDate.getMonth() <= 2 && todayDate.getDate() <= 31) {
    nextQuarterDate = getFirstQuarterOfYear(new Date().getFullYear())
    secondYearDate = getFirstQuarterOfYear(new Date().getFullYear() + 1)
    thirdYearDate = getFirstQuarterOfYear(new Date().getFullYear() + 2)
  }
  else {
    nextQuarterDate = getFirstQuarterOfYear(new Date().getFullYear() + 1)
    secondYearDate = getFirstQuarterOfYear(new Date().getFullYear() + 2)
    thirdYearDate = getFirstQuarterOfYear(new Date().getFullYear() + 3)
  }

  data.liveData.data.map((app) => {
    app.valueToTake = app.sourcing
  })

  data.nextYearData.data.map((app) => {
    if (
      app.cloudTargetSourcing != null
      && app.cloudTargetDate.getFullYear() < nextQuarterDate.getFullYear()
    ) {
      app.valueToTake = app.cloudTargetSourcing
    }
    else {
      app.valueToTake = app.sourcing
    }
  })

  data.secondYearData.data.map((app) => {
    if (
      app.cloudTargetSourcing != null
      && app.cloudTargetDate.getFullYear() < secondYearDate.getFullYear()
    ) {
      app.valueToTake = app.cloudTargetSourcing
    }
    else {
      app.valueToTake = app.sourcing
    }
  })

  data.thirdYearData.data.map((app: ApplicationModel) => {
    if (
      app.cloudTargetSourcing != null
      && app.cloudTargetDate.getFullYear() < thirdYearDate.getFullYear()
    ) {
      app.valueToTake = app.cloudTargetSourcing
    }
    else {
      app.valueToTake = app.sourcing
    }
  })

  return data
}

export const generatePreviewWrapper = (): PreviewWrapper[] => {
  const tempPreviewWrapperArray: PreviewWrapper[] = []
  let tempDate: Date
  for (let i = 0; i <= 3; i++) {
    if (i == 0) {
      tempDate = new Date()
      const previewVraper: PreviewWrapper = {
        date: tempDate,
        data: []
      }
      tempPreviewWrapperArray.push(previewVraper)
    }
    else {
      tempDate = new Date()
      tempDate = new Date(tempDate.getFullYear() + i, 2, 31)
      const previewVraper: PreviewWrapper = {
        date: tempDate,
        data: []
      }
      tempPreviewWrapperArray.push(previewVraper)
    }
  }

  return tempPreviewWrapperArray
}

export const generateDomainWrapper = (tempPreviewWrapperArray: PreviewWrapper[]): DomainWrapper[] => {
  const tempDomainWrapper: DomainWrapper[] = []

  for (const el of tempPreviewWrapperArray) {
    const temp: DomainWrapper = {
      date: el.date,
      categories: generateDomainSections(el.data)

    }

    tempDomainWrapper.push(temp)
  }

  return tempDomainWrapper
}

export const generateDomainSections = (data: ApplicationModel[]): DomainCategoryModel[] => {
  const tempDomainSections: DomainCategoryModel[] = JSON.parse(JSON.stringify(DOMAIN_CATEGORIES))
  data.forEach((app) => {
    tempDomainSections.forEach((section) => {
      if (app.domainTag.toLowerCase() == section.categoryName.toLowerCase()) {
        section.domains.forEach((domain) => {
          if (domain.domainName == app.domain) {
            domain.apps.push(app)
          }
        }
        )
      }
    })
  })
  return tempDomainSections
}

const matchBarChar = (model: BarChartModel[], type: string, value: number, dataIndex: number) => {
  if (type == 'internalHosting') {
    // Datacenter
    model[0].data[dataIndex] += value
  }
  else if (
    type == 'ExternalApplicationHosting'
    || type == 'ExternalPlatformHosting'
    || type == 'ExternalInfraHosting'
    || type == 'ExtAppHosting'
    || type == 'ExtInfraHosting'
    || type == 'ExtPlatformHosting'
  ) {
    // External Hosting
    model[1].data[dataIndex] += value
  }
  else if (
    type == 'saas'
    || type == 'paas'
    || type == 'iaas'
    || type == 'CloudIaaS'
    || type == 'CloudPaaS'
    || type == 'SaaS'
  ) {
    // Cloud
    model[2].data[dataIndex] += value
  }
  else if (type == 'Replacement') {
    // Replacement
    model[3].data[dataIndex] += value
  }
}

export const prepareBarChartData = (data: LoadDataResult) => {
  const tempArray: BarChartModel[] = JSON.parse(JSON.stringify(CLOUD_JOURNEY_BAR))

  data.liveData.data.forEach((app) => {
    matchBarChar(tempArray, app.valueToTake, 1, 0)
  })

  data.nextYearData.data.forEach((app) => {
    matchBarChar(tempArray, app.valueToTake, 1, 1)
  })

  data.secondYearData.data.forEach((app) => {
    matchBarChar(tempArray, app.valueToTake, 1, 2)
  })

  data.thirdYearData.data.forEach((app) => {
    matchBarChar(tempArray, app.valueToTake, 1, 3)
  })

  return tempArray
}

export const prepareSourcingPieData = (data: LoadDataResult, sourcing: any) => {
  const pieData: {
    liveData: PieModel[]
    nextYearData: PieModel[]
    secondYearData: PieModel[]
    thirdYearData: PieModel[]
  } = {
    liveData: [],
    nextYearData: [],
    secondYearData: [],
    thirdYearData: []
  }

  let tempArray: PieModel[] = JSON.parse(JSON.stringify(sourcing))

  data.liveData.data.forEach((element) => {
    const tempIndex = tempArray.findIndex(el => el.type == element.sourcing)
    if (tempIndex >= 0) {
      tempArray[tempIndex].value += 1
    }
  })
  tempArray = tempArray.filter(pieSection => pieSection.value !== 0)
  pieData.liveData = tempArray;

  // Future years
  [data.nextYearData, data.secondYearData, data.thirdYearData].forEach((yearData, index) => {
    let tempArray: PieModel[] = JSON.parse(JSON.stringify(sourcing))
    yearData.data.forEach((element) => {
      let tempIndex: number | null = null

      const originalValueToTake = element.valueToTake

      if (originalValueToTake != null) {
        const lowerCaseValue = originalValueToTake.toLowerCase()

        switch (lowerCaseValue) {
          case 'saas':
            tempIndex = tempArray.findIndex(el => el.type === 'saas')
            break
          case 'paas':
            tempIndex = tempArray.findIndex(el => el.type === 'paas')
            break
          case 'cloudpaas':
            tempIndex = tempArray.findIndex(el => el.type === 'paas')
            break
          case 'cloudiaas':
            tempIndex = tempArray.findIndex(el => el.type === 'iaas')
            break
          case 'iaas':
            tempIndex = tempArray.findIndex(el => el.type === 'iaas')
            break
          case 'extapphosting':
            tempIndex = tempArray.findIndex(el => el.type === 'ExternalApplicationHosting')
            break
          case 'externalplatformhosting':
            tempIndex = tempArray.findIndex(el => el.type === 'ExternalPlatformHosting')
            break
          case 'extplatformhosting':
            tempIndex = tempArray.findIndex(el => el.type === 'ExternalPlatformHosting')
            break
          case 'extinfrahosting':
            tempIndex = tempArray.findIndex(el => el.type === 'ExternalInfraHosting')
            break
          case 'rehosting':
            tempIndex = tempArray.findIndex(el => el.type === 'Rehosting')
            break
          case 'replacement':
            tempIndex = tempArray.findIndex(el => el.type === 'Replacement')
            break
          case 'internalhosting':
            tempIndex = tempArray.findIndex(el => el.type === 'internalHosting')
            break

          default:
            tempIndex = tempArray.findIndex(el => el.type === originalValueToTake)
            if (tempIndex < 0) {
              tempIndex = null
            }
            break
        }
      }
      else {
        tempIndex = null
      }

      if (tempIndex !== null && tempIndex >= 0) {
        tempArray[tempIndex].value += 1
      }
      tempIndex = null
    })
    tempArray = tempArray.filter(pieSection => pieSection.value !== 0)
    switch (index) {
      case 0:
        pieData.nextYearData = tempArray
        break
      case 1:
        pieData.secondYearData = tempArray
        break
      case 2:
        pieData.thirdYearData = tempArray
        break
    }
  })

  return pieData
}

export const exportData = (data: LoadDataResult) => {
  const parseForExport = (applications: ApplicationModel[]) => {
    const exportArray: any[] = []
    applications.map((app) => {
      const application = {
        id: app.id,
        name: app.name,
        domain: app.domain,
        domainTag: app.domainTag,
        sourcing: app.sourcing,
        cloudTargetSouring: app.cloudTargetSourcing,
        cloudTargetDate: moment(app.cloudTargetDate).format('YYYY'),
        valueToTake: app.valueToTake
      }
      exportArray.push(application)
    })
    return exportArray
  }

  const wb: XLSX.WorkBook = XLSX.utils.book_new()

  let ws_live: XLSX.WorkSheet
  let ws_first_quarter: XLSX.WorkSheet
  let ws_second_quarter: XLSX.WorkSheet
  let ws_third_quarter: XLSX.WorkSheet

  ws_live = XLSX.utils.json_to_sheet(parseForExport(data.liveData.data))

  ws_first_quarter = XLSX.utils.json_to_sheet(parseForExport(data.nextYearData.data))

  ws_second_quarter = XLSX.utils.json_to_sheet(parseForExport(data.secondYearData.data))

  ws_third_quarter = XLSX.utils.json_to_sheet(parseForExport(data.thirdYearData.data))

  XLSX.utils.book_append_sheet(wb, ws_live, `Live data (${moment(data.liveData.date).format('DD.MM.YYYY')})`)
  XLSX.utils.book_append_sheet(wb, ws_first_quarter, `Q1_${moment(data.nextYearData.date).format('YYYY')}`)
  XLSX.utils.book_append_sheet(wb, ws_second_quarter, `Q1_${moment(data.secondYearData.date).format('YYYY')}`)
  XLSX.utils.book_append_sheet(wb, ws_third_quarter, `Q1_${moment(data.thirdYearData.date).format('YYYY')}`)

  XLSX.writeFile(wb, `export_${moment(data.liveData.date).format('DD_MM_YYYY')}.xlsx`)
}
