import type { ApplicationModel, ExportModel } from './models'
import * as XLSX from 'xlsx'
import { XLSX_WB_DESCRIPTION, XLSX_WB_FILE_NAME_CSV, XLSX_WB_FILE_NAME_XLSX } from './constants'

export const FACTSHEET_QUERY_FIELDS = [
  'id',
  'displayName',
  'alias',
  'name',
  'level',
  'type',
  'applicationType ',
  'status',
  'grcId {externalId}',
  'businessCriticality',
  'businessCriticalityDescription',
  'finmaRelevancy',
  'CloudTarget',
  'CloudTargetDate',
  'CloudTransitionComment',
  'saaSClassification',
  'saaSClassificationComment',
  'infoSecConfidentiality',
  'infoSecIntegrity',
  'infoSecAvailability',
  'userAdmin',
  'commentOnUserAdmin',
  'relToSuccessor {edges {node {factSheet {displayName id}}}}',
  'description',
  'plattform',
  'sourcing',
  'softwareType',
  'UserAccessDataCategory',
  'UserGroupAccessAttributes',
  'DataSubjectCategory',
  'PersonalData',
  'SensitivePersonalData',
  'additionalCategoriesAttributes',
  'gdprPurposeOfProcessing',
  'gdprClientFacing',
  'gdprDataPrivacyComment',
  'gdprDataPrivacyAgreement',
  'gdprDataPrivacyStatement',
  'gdprCrossBorderTransfer',
  'gdprCrossBorderComment',
  'externalSupplier',
  'rententionPeriods',
  'recipientCategories',
  'recipientComment',
  'gdprRating',
  'categoriesDataSubject',
  'categoriesPersonalData',
  'infoSecConfidentiality',
  'infoSecIntegrity',
  'infoSecAvailability',
  'tags {id name color tagGroup {id}}',
  'lifecycle {asString phases {phase startDate}}',
  'relApplicationToDomain { edges {node {factSheet {displayName}}}}',
  'relApplicationToBusinessCapability {edges {node {factSheet {displayName name}}}}',
  'relApplicationToProcess {edges {node {factSheet {id displayName}}}}',
  'relApplicationToUserGroup {edges {node {factSheet {id displayName name}}}}'
]

export const exportData = (data: ApplicationModel[], xlsx: boolean) => {
  const wb: XLSX.WorkBook = XLSX.utils.book_new()
  let ws: XLSX.WorkSheet

  const temp: ExportModel[] = []

  const DATE_TIME_NULL: Date = new Date('1970-01-01')

  data.forEach((item) => {
    const tempItem: ExportModel = {
      Applikation: item.name,
      GRC_ID: item.grcId,
      Level: item.level,
      Data_Protection_Rating: item.dpDataProtectionRating,
      Description: item.description,
      Sourcing: item.hosting,
      Application_Domain: item.applicationDomains != null ? item.applicationDomains.join('; ') : '',
      Business_Capabilities: item.businessCapabilities != null ? item.businessCapabilities.join('; ') : '',
      Processes: item.processes != null ? item.processes.join('; ') : '',
      Business_Areas: item.businessAreas != null ? item.businessAreas.join('; ') : '',
      User_Group_Categories: item.dpUserAccessDataCategory != null ? item.dpUserAccessDataCategory.join('; ') : '',
      User_Group_Access_Attributes: item.dpUserGroupAccessAttributes != null ? item.dpUserGroupAccessAttributes.join('; ') : '',
      Data_Subject_Categories: item.dpDataSubjectCategory != null ? item.dpDataSubjectCategory.join('; ') : '',
      Personal_Data_Attributes: item.dpPersonalData != null ? item.dpPersonalData.join('; ') : '',
      Sensitive_Personal_Data_Attributes: item.dpSensitivePersonalData != null ? item.dpSensitivePersonalData.join('; ') : '',
      Other_Categories_and_Attributes: item.dpAdditionalCategoriesAttributes,
      Purpose_of_Processing: item.purposeOfProcessing,
      Client_and_Partner_Access_via_Internet: item.dpGdprClientFacing,
      Comments: item.dpGdprDataPrivacyComment,
      Data_Processing_Agreement: item.dpGdprDataPrivacyAgreement,
      Data_Privacy_Agreement_Reference: item.dpGdprDataPrivacyStatement,
      Cross_Border_data_transer_access: item.dpGdprCrossBorderTransfer,
      Data_Tranfer_Access_to_Countries: item.dpGdprCrossBorderComment,
      External_Supplier_Provider: item.dpExternalSupplier,
      Retention_Periods: item.dpRententionPeriods,
      Recipient_Categories: item.dpRecipientCategories != null ? item.dpRecipientCategories.join('; ') : '',
      Recipient_Category_Comment: item.dpRecipientComment,
      Alias: item.alias,
      Business_Criticality: item.businessCriticality,
      Business_Criticality_Description: item.businessCriticalityDescription,
      Finma_Relevancy: item.finmaRelevancy,
      Cloud_Target: item.CloudTarget,
      Cloud_Target_Date: item.CloudTargetDate,
      Cloud_Transition_Comment: item.CloudTransitionComment,
      SaaS_Classification: item.saaSClassification,
      SaaS_Classification_Comment: item.saaSClassificationComment,
      InfoSec_Confidentiality: item.infoSecConfidentiality,
      InfoSec_Integrity: item.infoSecIntegrity,
      InfoSec_Availability: item.infoSecAvailability,
      User_Admin: item.userAdmin,
      Successors: item.successors,
      Lifecycle: item.lifeCycle,
      Lifecycle_Phase_In_Start: undefined,
      Lifecycle_Active_Start: undefined,
      Lifecycle_Phase_Out_Start: undefined,
      Lifecycle_End_Of_Life_Start: undefined
    }

    item.lifecycleRaw?.phases.map((phase: any) => {
      if (phase.phase == 'phaseIn') {
        tempItem.Lifecycle_Phase_In_Start
                    = phase.startDate != undefined
            ? new Date(phase.startDate)
            : DATE_TIME_NULL
      }
      if (phase.phase == 'active') {
        tempItem.Lifecycle_Active_Start
                    = phase.startDate != undefined
            ? new Date(phase.startDate)
            : DATE_TIME_NULL
      }
      if (phase.phase == 'phaseOut') {
        tempItem.Lifecycle_Phase_Out_Start
                    = phase.startDate != undefined
            ? new Date(phase.startDate)
            : DATE_TIME_NULL
      }
      if (phase.phase == 'endOfLife') {
        tempItem.Lifecycle_End_Of_Life_Start
                    = phase.startDate != undefined
            ? new Date(phase.startDate)
            : DATE_TIME_NULL
      }
      return phase
    })

    temp.push(tempItem)
    /* if (item.applications !== undefined) {
          item.applications.forEach(app => {
            let tempChild: ExportModel = {
              ID: app.id,
              Applikation: app.name,
              Level: app.level,
              Lifecycle_Phase: app.lifeCycle,
              Description: app.description,
              Sourcing: app.hosting,
              Application_Domain: app.applicationDomains != null ? app.applicationDomains.join("; ") : "",
              Business_Capabilities: app.businessCapabilities != null ? app.businessCapabilities.join("; ") : "",
              Processes: app.processes != null ? app.processes.join("; ") : "",
              Business_Areas: app.businessAreas != null ? app.businessAreas.join("; ") : "",
              Data_Protection_Rating: app.dpDataProtectionRating,
              Purpose_of_Processing: app.purposeOfProcessing,
              User_Group_Categories: app.dpUserAccessDataCategory != null ? app.dpUserAccessDataCategory.join("; ") : "",
              User_Group_Access_Attributes: app.dpUserGroupAccessAttributes != null ? app.dpUserGroupAccessAttributes.join("; ") : "",
              Data_Subject_Categories: app.dpDataSubjectCategory != null ? app.dpDataSubjectCategory.join("; ") : "",
              Personal_Data_Attributes: app.dpPersonalData != null ? app.dpPersonalData.join("; ") : "",
              Sensitive_Personal_Data_Attributes: app.dpSensitivePersonalData != null ? app.dpSensitivePersonalData.join("; ") : "",
              Other_Categories_and_Attributes: app.dpAdditionalCategoriesAttributes,
              Client_and_Partner_Access_via_Internet: app.dpGdprClientFacing,
              Comments: app.dpGdprDataPrivacyComment,
              Data_Processing_Agreement: app.dpGdprDataPrivacyAgreement,
              Data_Privacy_Agreement_Reference: app.dpGdprDataPrivacyStatement,
              Cross_Border_data_transer_access: app.dpGdprCrossBorderTransfer,
              Data_Tranfer_Access_to_Countries: app.dpGdprCrossBorderComment,
              External_Supplier_Provider: app.dpExternalSupplier,
              Retention_Periods: app.dpRententionPeriods,
              Recipient_Categories: app.dpRecipientCategories != null ? app.dpRecipientCategories.join("; ") : "",
              Recipient_Category_Comment: app.dpRecipientComment
            }

            temp.push(tempChild)
          })
        } */
  })

  console.log('temp export', temp)
  ws = XLSX.utils.json_to_sheet(temp)
  XLSX.utils.sheet_add_aoa(ws, [[]])

  if (xlsx) {
    const linkStyle = {
      font: { underline: true, color: { rgb: '0563C1' } }
    }

    for (let i = 0; i < temp.length; i++) {
      const index = i + 2

      ws[`A${index}`].s = linkStyle
      // ADD LINKS
      ws[`A${index}`].l = {
        Target: `https://helvetia.leanix.net/helvetia/factsheet/Application/${data[i].id}`
      }

      ws[`B${index}`].l = {
        Target: `https://grc.helvetia.ch/Lists/assets/fd_Application_Display.aspx?ID=${data[i].grcId}`
      }
      ws[`B${index}`].s = linkStyle
    }
  }
  XLSX.utils.book_append_sheet(wb, ws, XLSX_WB_DESCRIPTION)
  xlsx ? XLSX.writeFile(wb, XLSX_WB_FILE_NAME_XLSX) : XLSX.writeFile(wb, XLSX_WB_FILE_NAME_CSV)
}
