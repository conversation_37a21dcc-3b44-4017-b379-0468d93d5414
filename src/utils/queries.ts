export const QUERY_CCC_DASHBOARD_FILTER = `
query allFactSheetsQuery($filter: FilterInput!, $sortings: [Sorting]) {
    allFactSheets(filter: $filter, sort: $sortings) {
      totalCount
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ... on Application {
            displayName
            applicationType
            type
            id
            sourcing
            CloudTarget
            CloudTargetDate
            lifecycle {
              asString
            }
            relApplicationToDomain {
              edges {
                node {
                  id
                  factSheet {
                    displayName
                    ... on Domain {
                      category
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
`;


export const FILTER_CCC_DASHBOARD_FILTER = `
{
    "filter": {
      "responseOptions": {
        "maxFacetDepth": 5
      },
      "facetFilters": [
        {
          "facetKey": "FactSheetTypes",
          "operator": "OR",
          "keys": [
            "Application"
          ]
        },
        {
          "facetKey": "hierarchyLevel",
          "operator": "OR",
          "keys": [
            "1"
          ]
        },
        {
          "facetKey": "lifecycle",
          "operator": "OR",
          "keys": [
            "phaseIn",
            "active",
            "phaseOut"
          ],
          "dateFilter": {
            "from": "fromDate",
            "to": "toDate",
            "type": "POINT"
          }
        },
        {
          "facetKey": "sourcing",
          "operator": "NOR",
          "keys": [
            "desktopMobile"
          ]
        },
        {
          "facetKey": "applicationType",
          "operator": "NOR",
          "keys": [
            "externalPartnerApp"
          ]
        },
        {
          "facetKey": "relApplicationToUserGroup",
          "operator": "OR",
          "keys": [
            "e8ec9a5c-a967-44f8-9fb8-0fd46830363c",
            "71d7e010-921a-41a0-aaca-01dc693316f5"
          ],
          "relationFieldsFilter": []
        }
      ],
      "ids": []
    },
    "sortings": [
      {
        "key": "displayName",
        "order": "asc"
      }
    ]
  }
`

export const QUERY_SOURCING = `query getApplicationSourcing {
    __type(name: "ApplicationSourcing") {
       enumValues {
          name
        }
      }
  }`
