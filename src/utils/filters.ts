// Funktionen für Filter
import type { Application, RessortApplication } from '@app/model'

export const applyTextSearchFilter = (apps: Application[], searchText: string): Application[] => {
  if (!searchText || searchText.length === 0) {
    return apps
  }

  return apps.filter((app) => {
    // Prü<PERSON>, ob der Name der Anwendung den Suchtext enthält
    const nameMatches = app.name.toLowerCase().includes(searchText.toLowerCase())

    // Prü<PERSON>, ob eines der Kinder den Suchtext enthält
    const childMatches = app.children && app.children.length > 0
      && app.children.some(child => child.name.toLowerCase().includes(searchText.toLowerCase()))

    return nameMatches || childMatches
  })
}
export const applyCorpUnitFilter = (apps: Application[], corpUnit: string): Application[] => {
  if (corpUnit === 'All') {
    return apps
  }

  // Direktes Filtern der übereinstimmenden Anwendungen
  const directMatches = apps.filter(app => app.corpUnit === corpUnit)

  // Kinder rausfiltern, die nicht dem ausgewählten Corporate Unit entsprechen
  const directMatchesWithChildren = directMatches.map((app) => {
    if (app.children && app.children.length > 0) {
      const relevantChildren = app.children.filter(child => child.corpUnit === corpUnit)
      return {
        ...app,
        children: relevantChildren.length > 0 ? relevantChildren : undefined
      }
    }
    return app
  })

  // Filtern der Anwendungen mit übereinstimmenden Kindern
  const appsWithValidChilds: Application[] = []

  apps.forEach((app) => {
    if (app.children && app.children.length > 0) {
      const relevantChilds = app.children.filter(child => child.corpUnit === corpUnit)

      if (relevantChilds.length > 0 && !directMatchesWithChildren.some(f => f.id === app.id)) {
        appsWithValidChilds.push({
          ...app,
          relevantForCalculations: false,
          children: relevantChilds
        })
      }
    }
  })

  // Kombinieren der direkten Übereinstimmungen und Anwendungen mit übereinstimmenden Kindern
  const combinedApps = [...directMatchesWithChildren, ...appsWithValidChilds]

  // Berechnung des "baddestScore" für jede Anwendung
  combinedApps.forEach((application) => {
    if (application.children && application.children.length > 0) {
      // Finde den niedrigsten "completed" Wert unter den Kindern
      const lowestChildScore = Math.min(...application.children.map(child => child.completed))
      application.baddestScore = Math.min(lowestChildScore, application.completed)
    }
    else {
      application.baddestScore = application.completed
    }
  })

  return combinedApps
}

export const applyITRessortFilter = (apps: Application[], itResort: string): Application[] => {
  if (!itResort || itResort === '') {
    return apps
  }

  // Direktes Filtern der übereinstimmenden Anwendungen
  const directMatches = apps
    .filter(app => (app as RessortApplication).itRessort?.id === itResort)
    .map((app) => {
      const newApp = { ...app }
      if (app.children && app.children.length > 0) {
        const relevantChildren = app.children.filter(
          child => (child as RessortApplication).itRessort?.id === itResort
        )
        newApp.children = relevantChildren.length > 0 ? relevantChildren : app.children
      }
      return newApp
    })

  // Anwendungen mit Kindern im ausgewählten IT-Ressort finden
  const appsWithRelevantChildren: Application[] = []

  apps.forEach((app) => {
    if (app.children && app.children.length > 0) {
      const relevantChilds = app.children.filter(
        child => (child as RessortApplication).itRessort?.id === itResort
      )

      if (relevantChilds.length > 0 && !directMatches.some(f => f.id === app.id)) {
        appsWithRelevantChildren.push({
          ...app,
          relevantForCalculations: false,
          children: relevantChilds
        })
      }
    }
  })

  // Kombinieren der direkten Übereinstimmungen und Anwendungen mit übereinstimmenden Kindern
  const combinedApps = [...directMatches, ...appsWithRelevantChildren]

  // Berechnung des "baddestScore" für jede Anwendung
  combinedApps.forEach((application) => {
    if (application.children && application.children.length > 0) {
      // Finde den niedrigsten "completed" Wert unter den Kindern
      const lowestChildScore = Math.min(...application.children.map(child => child.completed))
      application.baddestScore = Math.min(lowestChildScore, application.completed)
    }
    else {
      application.baddestScore = application.completed
    }
  })

  return combinedApps
}

export const applyCorpUnitFilterToITRessortResults = (apps: Application[], corpUnit: string): Application[] => {
  if (corpUnit === 'All' || !corpUnit || corpUnit === '') {
    return apps
  }

  const filteredApps = apps.map((app) => {
    const newApp = { ...app }
    if (app.children && app.children.length > 0) {
      const relevantChildren = app.children.filter(child => child.corpUnit === corpUnit)
      newApp.children = relevantChildren.length > 0 ? relevantChildren : undefined
    }
    return newApp
  }).filter(app =>
    app.corpUnit === corpUnit
    || (app.children && app.children.length > 0)
  )

  // Berechnung des "baddestScore" für jede Anwendung
  filteredApps.forEach((application) => {
    if (application.children && application.children.length > 0) {
      const lowestChildScore = Math.min(...application.children.map(child => child.completed))
      application.baddestScore = Math.min(lowestChildScore, application.completed)
    }
    else {
      application.baddestScore = application.completed
    }
  })

  return filteredApps
}
