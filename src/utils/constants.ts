export const BusinessCriticalities = {
  NO_CRITICALITY: {
    label: "No criticality",
    value: "-",
  },
  MISSION_CRITICAL: {
    label: "Mission critical",
    value: "missionCritical",
    color: "#ff4560",
  },
  BUSINESS_CRITICAL: {
    label: "Business critical",
    value: "businessCritical",
    color: "#feb019",
  },
  BUSINESS_OPERATIONAL: {
    label: "Business operational",
    value: "businessOperational",
    color: "yellow",
  },
  IT_FUNDAMENTAL_SERVICE: {
    label: "IT Fundamental Service",
    value: "administrativeService",
    color: "#cf1322",
  },
};

export const SORT_ORDER = [
  BusinessCriticalities.IT_FUNDAMENTAL_SERVICE.value,
  BusinessCriticalities.MISSION_CRITICAL.value,
  BusinessCriticalities.BUSINESS_CRITICAL.value,
  BusinessCriticalities.BUSINESS_OPERATIONAL.value,
  BusinessCriticalities.NO_CRITICALITY.value,
  "",
];

export const ITRessortConstant = {
  FES: {
    label: "FES",
  },
  COS: {
    label: "COS",
  },
  CITS: {
    label: "CITS",
  },
  I_O: {
    label: "I&O",
  },
  D_A: {
    label: "D&A",
  },
  ITSM: {
    label: "ITSM",
  },
  CTO: {
    label: "CTO",
  },
  AM: {
    label: "AM",
  },
  FM: {
    label: "FM",
  },
  BIZ: {
    label: "BIZ",
  },
  EXT: {
    label: "EXT.",
  },
  CSS: {
    label: "CSS",
  },
  NOT_ASSIGNED: {
    label: "not assigned",
  },
};

export const Current_Sourcings = [
  {
    text: "Desktop / Mobile",
    value: "desktopMobile",
  },

  {
    text: "Public Cloud IaaS",
    value: "iaas",
  },
  {
    text: "Public Cloud PaaS",
    value: "paas",
  },
  {
    text: "SaaS",
    value: "saas",
  },
  {
    text: "Internal Hosting",
    value: "internalHosting",
  },
  {
    text: "Ext. Infra. Hosting ",
    value: "ExternalInfraHosting",
  },
  {
    text: "Ext. Platform Hosting ",
    value: "ExternalPlatformHosting",
  },
  {
    text: "Ext. App Hosting",
    value: "ExternalApplicationHosting",
  },
  {
    text: "No Sourcing",
    value: " ",
  },
];

export const XLSX_WB_DESCRIPTION = "LeanIX_Export";
export const XLSX_WB_FILE_NAME_XLSX = "export.xlsx";
export const XLSX_WB_FILE_NAME_CSV = "export.csv";

export const OR: any /*lxr.FacetKeyOperator.OR*/ = "OR";
