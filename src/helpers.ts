const getEmailExtension = (email: string): string | null => {
  const match = email.match(/\.(?<extension>[^.]*)$/)
  if (match && match.groups && match.groups.extension) {
    return match.groups.extension // Gibt die Endung ohne den Punkt zurück
  }
  else {
    return null // Falls keine Endung gefunden wird
  }
}

export const getITRessortsByMailExtension = (mail: string) => {
  const mailExtension = `${getEmailExtension(mail)}`.toLowerCase()
  if (mailExtension.length > 0) {
    if (mailExtension === 'ch') { return ['CH', 'CF'] }
    else if (mailExtension === 'at') { return ['AT'] }
    else if (mailExtension === 'it') { return ['ITA'] }
    else if (mailExtension === 'de') { return ['DE'] }
  }
  return ['CH', 'CF']
}
