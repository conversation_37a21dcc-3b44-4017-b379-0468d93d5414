import type {
  Application,
  BaseApplication,
  DataQualityApplication,
  FilterOption,
  ITRessort,
  Lifecycle,
  RessortApplication
} from '@app/model'

export const getCorpUnit = (name: string): string => {
  const a1 = name.match('^([^\x20]{2,3})\x20?-\x20?(.+)$')
  if (a1 !== null) {
    return a1[1]
  }
  else {
    return ''
  }
}

export const getUserGroupOwner = (relApplicationToUserGroup: any): string | undefined => {
  const ownerEdge = relApplicationToUserGroup.edges.find((edge: any) => edge.node.usageType === 'owner')
  if (ownerEdge) {
    return ownerEdge.node.factSheet.displayName
  }
  return undefined
}

export const getUserGroupAllOwner = (relApplicationToUserGroup: any): string[] => {
  const owners: string[] = []
  relApplicationToUserGroup.edges?.filter((edge: any) => edge.node.usageType === 'owner').map((edge: any) => {
    owners.push(edge.node.factSheet.displayName)
  })

  return owners
}

export const parseBusinessCapability = (relation: any) => {
  const bc: string[] = []

  relation.edges.map((r: any) => bc.push(r.node.factSheet.displayName))

  if (bc.length === 0) { return undefined }
  else { return bc }
}

export const parseDomain = (relation: any) => {
  const domains: string[] = []

  relation.edges.map((r: any) => domains.push(r.node.factSheet.displayName))

  if (domains.length === 0) { return undefined }
  else { return domains }
}

export const getApplicationRoles = (subscriptions: any): {
  applicationResponsible: string[]
  applicationOwnerTechnical: string[]
} => {
  const applicationResponsibleRole = 'Application Responsible (functional)'
  const applicationOwnerTechnicalRole = 'Application Owner (technical)'

  const result: { applicationResponsible: string[], applicationOwnerTechnical: string[] } = {
    applicationResponsible: [],
    applicationOwnerTechnical: []
  }

  subscriptions.edges.forEach((edge: any) => {
    const user = edge.node.user
    const roles = edge.node.roles

    roles.forEach((role: any) => {
      if (role.name === applicationResponsibleRole) {
        result.applicationResponsible.push(user.email)
      }
      if (role.name === applicationOwnerTechnicalRole) {
        result.applicationOwnerTechnical.push(user.email)
      }
    })
  })

  return result
}

export const getCompletedScore = (missingFields: number) => {
  if (missingFields === 0) { return 100 }
  if (missingFields === 1) { return 66 }
  if (missingFields === 2) { return 33 }
  else { return 0 }
}

export const validateDataQuality = (app: DataQualityApplication) => {
  if (app.allBusinessAreaOwners.length === 0) {
    app.validBusinessAreaOwner = false
  }
  else {
    app.allBusinessAreaOwners.map((owner) => {
      let parsed = ''
      const parsedParentBusinessAreaOwner = owner.split('/')
      if (parsedParentBusinessAreaOwner.length > 0) {
        parsed = parsedParentBusinessAreaOwner[0].toLowerCase().trim()
      }
      if (!owner.includes('/')) {
        parsed = owner.toLowerCase().trim()
      }

      if (app.corpUnit.toLowerCase() !== parsed) {
        app.validBusinessAreaOwner = false
      }
    })
  }

  if (!app.validBusinessAreaOwner) { app.missingFields++ }

  if (app.hasL3) { app.missingFields++ }

  if (!app.name) { app.missingFields++ }
  if (!app.description || app.description.length < 50) { app.missingFields++ }
  if (!app.sourcing || app.sourcing.length === 0) { app.missingFields++ }
  if (!app.softwareType || app.softwareType.length === 0) { app.missingFields++ }
  if (!app.guiType || app.guiType.length === 0) { app.missingFields++ }
  if (!app.lifecycle || app.lifecycle.phases.filter(p => p.phase === 'active' || p.phase === 'phaseIn' || p.phase === 'phaseOut').length === 0) { app.missingFields++ }
  if (!app.domain || app.domain.length === 0) { app.missingFields++ }
  // if (!app.businessCapabilities || app.businessCapabilities.length === 0) app.missingFields++;
  if (!app.appOwnerTechnical || app.appOwnerTechnical.length === 0) { app.missingFields++ }
  if (!app.appOwnerFunctional || app.appOwnerFunctional.length === 0) { app.missingFields++ }

  // if (app.name === "AT-ANJA") console.log(app, "TESSST")

  app.completed = getCompletedScore(app.missingFields)
  app.baddestScore = app.completed
}

export const statColor = (v: number) => v >= 95 ? 'green' : v >= 60 ? 'orange' : 'red'
export const missingValuesColor = (v: number) => v === 0 ? 'green' : v <= 2 ? 'orange' : 'red'

export const createFilters = (dataIndex: string, filteredApps: BaseApplication[]): FilterOption[] => {
  const values = new Set<string | number | boolean>()

  filteredApps.forEach((app) => {
    const mainValue = app[dataIndex as keyof BaseApplication]
    if (mainValue !== undefined && mainValue !== null) {
      if (Array.isArray(mainValue)) {
        mainValue.forEach((val) => {
          if (typeof val === 'string' || typeof val === 'number' || typeof val === 'boolean') {
            values.add(val)
          }
        })
      }
      else if (typeof mainValue === 'string' || typeof mainValue === 'number' || typeof mainValue === 'boolean') {
        values.add(mainValue)
      }
      else if (typeof mainValue === 'object' && dataIndex === 'lifecycle') {
        // Spezieller Fall für Lifecycle-Objekte
        values.add(JSON.stringify(mainValue))
      }
    }

    if (app.children) {
      app.children.forEach((child) => {
        const childValue = child[dataIndex as keyof Application]
        if (childValue !== undefined && childValue !== null) {
          if (Array.isArray(childValue)) {
            childValue.forEach((val) => {
              if (typeof val === 'string' || typeof val === 'number' || typeof val === 'boolean') {
                values.add(val)
              }
            })
          }
          else if (typeof childValue === 'string' || typeof childValue === 'number' || typeof childValue === 'boolean') {
            values.add(childValue)
          }
          else if (typeof childValue === 'object' && dataIndex === 'lifecycle') {
            // Spezieller Fall für Lifecycle-Objekte
            values.add(JSON.stringify(childValue))
          }
        }
      })
    }
  })

  const filters: FilterOption[] = Array.from(values).map(value => ({
    text: value === null || value === undefined || value === ''
      ? 'No Value'
      : dataIndex === 'completed'
        ? `${value}%`
        : typeof value === 'string' && value.startsWith('{') ? JSON.parse(value).phase : value,
    value: value === null || value === undefined || value === '' ? 'no_value' : value
  }))

  // Überprüfen, ob "No Value" hinzugefügt werden muss
  const hasNoValueEntries = filteredApps.some((app) => {
    const fieldValue = app[dataIndex as keyof BaseApplication]
    return fieldValue === null || fieldValue === undefined || fieldValue === ''
      || (Array.isArray(fieldValue) && fieldValue.length === 0)
  })

  if (hasNoValueEntries && !filters.some(f => f.value === 'no_value')) {
    filters.push({
      text: 'No Value',
      value: 'no_value'
    })
  }

  if (dataIndex === 'completed') {
    return filters
  }
  else {
    return filters.sort((a, b) => {
      if (a.value === 'no_value') { return -1 }
      if (b.value === 'no_value') { return 1 }

      return String(a.text).localeCompare(String(b.text))
    })
  }
}

export const isSaaS = (app: Application) => {
  return app.sourcing === 'SaaS'
}
export const isHostingCountryRelevant = (app: RessortApplication) => {
  return app?.relApplicationToHostingProvider?.length > 0
}

export const getITResorts = async (): Promise<ITRessort[]> => {
  return lx.executeGraphQL(`
    {
  tagGroup(id:"6cc13132-a7f7-498f-a3ef-eb1d4c67a615"){
    tags {
      edges{
        node{
          id
          name
          factSheetCount
          description
        }
      }
    }
  }
}
    `).then(res => ([...res.tagGroup.tags.edges.map((t: any) => ({
    name: t.node.name,
    description: t.node.description,
    appCount: t.node.factSheetCount,
    id: t.node.id
  }))])).catch((err) => {
    return []
  })
}

export const getLifecyclePhase = (phase: string, lifecycle: Lifecycle | undefined) => {
  if (lifecycle && lifecycle.phases && lifecycle.phases.filter(p => p.phase === phase).length > 0) {
    return lifecycle.phases.filter(p => p.phase === phase)[0].startDate
  }
  return undefined
}
