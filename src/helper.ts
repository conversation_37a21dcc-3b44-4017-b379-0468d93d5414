import type { Application, Value } from '@app/models'
import * as XLSX from 'xlsx'
import { BUSINESS_AREAS } from './constants'

export const exportGroupApplications = (xlsx: boolean, apps: Application[]) => {
  const wb: XLSX.WorkBook = XLSX.utils.book_new()

  const allApps = apps.flatMap(app =>
    app.children?.length ? [app, ...app.children] : [app]
  )

  const data: any[] = [...allApps.map(app => ({
    ID: app.id,
    Name: app.name,
    Domain: app.domain?.label,
    Lifecycle: app.lifecycle,
    Level: app.level,
    ...Object.fromEntries(BUSINESS_AREAS.map(b => [b, app.businessAreas ? app.businessAreas.some((ba: Value) => ba?.label.includes(b)) ? 'TRUE' : 'FALSE' : 'FALSE']))
  }))]

  const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data)

  XLSX.utils.book_append_sheet(wb, ws_application, 'Group applications')

  XLSX.writeFile(wb, xlsx ? 'export.xlsx' : 'export.csv')
}

export const exportMarketUnitSpecificApplications = (xlsx: boolean, apps: Application[]) => {
  const wb: XLSX.WorkBook = XLSX.utils.book_new()

  const allApps = apps.flatMap(app =>
    app.children?.length ? [app, ...app.children] : [app]
  )
  console.log(allApps)

  const data: any[] = [...allApps.map(app => ({
    'ID': app.id,
    'Name': app.name,
    'Domain': app.domain?.label,
    'Platform': app?.platform,
    'Lifecycle': app?.lifecycle,
    'Level': app?.level,
    'Service BL Approach': app?.serviceBaseLineApproach,
    'Comment': app?.serviceBaseLineComment,
    'Snow ID': app?.snowId,
    'Projects': [...app?.projects.map(p => p.label)].join(', ')
  }))]

  const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data)

  XLSX.utils.book_append_sheet(wb, ws_application, 'Market Unit Specific Apps')

  XLSX.writeFile(wb, xlsx ? 'export.xlsx' : 'export.csv')
}

export const exportGroupApplicationsByMarketUnit = (xlsx: boolean, apps: Application[]) => {
  const wb: XLSX.WorkBook = XLSX.utils.book_new()

  const allApps = apps.flatMap(app =>
    app.children?.length ? [app, ...app.children] : [app]
  )
  console.log(allApps)

  const data: any[] = [...allApps.map(app => ({
    'ID': app.id,
    'Name': app.name,
    'Domain': app.domain?.label,
    'Platform': app?.platform,
    'Lifecycle': app?.lifecycle,
    'Level': app?.level,
    'Service BL Approach': app?.serviceBaseLineApproach,
    'Comment': app?.serviceBaseLineComment,
    'Snow ID': app?.snowId,
    'Projects': [...app?.projects.map(p => p.label)].join(', ')
  }))]

  const ws_application: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data)

  XLSX.utils.book_append_sheet(wb, ws_application, 'Apps by Market Unit')

  XLSX.writeFile(wb, xlsx ? 'export.xlsx' : 'export.csv')
}

export const sortRecursively = (items: Application[]) => {
  // Sortiere aktuelle Ebene
  items.sort((a, b) => {
    // Zuerst nach Domain sortieren
    const domainA = a.domain?.label || ''
    const domainB = b.domain?.label || ''

    if (domainA !== domainB) {
      return domainA.localeCompare(domainB)
    }

    // Bei gleicher Domain nach Name sortieren
    return a.name.localeCompare(b.name)
  })

  // Sortiere children rekursiv, falls vorhanden
  items.forEach((item) => {
    if (item.children && Array.isArray(item.children)) {
      sortRecursively(item.children)
    }
  })

  return items
}
