import type { Application, Value } from '@app/models'
import { ON_PREMISE_COMPONENT, SERVICE_BASELINE_APPROACH_TAG_ID } from './constants'

const getCorpUnit = (name: string): string => {
  const a1 = name.match('^([^\x20]{2,3})\x20?-\x20?(.+)$')
  if (a1 !== null) {
    return a1[1]
  }
  else {
    return ''
  }
}

export const parseApplications = (result: any[]) => {
  const applications: Application[] = []

  result.forEach((app: any) => {
    if (app.relApplicationToDomain.edges.length > 1) {
      console.log('TWO DOMAINS', app)
    }

    const domain: Value | undefined = app.relApplicationToDomain.edges.length > 0
      ? {
          value: app.relApplicationToDomain.edges[0].node.factSheet.id,
          label: app.relApplicationToDomain.edges[0].node.factSheet.displayName
        }
      : undefined

    const businessAreas: Value[] = app.relApplicationToUserGroup.edges.flatMap((userGroup: any) => {
      // Array für aktuelle UserGroup und deren parents
      const groups = []

      // Aktuelle UserGroup hinzufügen
      groups.push({
        label: userGroup.node.factSheet.name,
        value: userGroup.node.factSheet.id,
        level: userGroup.node.factSheet.level
      })

      // Parent UserGroups hinzufügen, falls vorhanden
      if (userGroup.node.factSheet.relToParent?.edges) {
        userGroup.node.factSheet.relToParent.edges.forEach((parent: any) => {
          groups.push({
            label: parent.node.factSheet.name,
            value: parent.node.factSheet.id,
            level: parent.node.factSheet.level
          })
        })
      }

      return groups
    })

    const serviceBaseLineApproach = app.tags.filter((t: any) => t.tagGroup?.id === SERVICE_BASELINE_APPROACH_TAG_ID).length > 0
      ? app.tags.filter((t: any) => t.tagGroup?.id === SERVICE_BASELINE_APPROACH_TAG_ID)[0].name
      : ''

    // not relevant currently
    const hostingCH = app.relApplicationToITComponent.edges.filter((c: any) => c.node.factSheet.id === ON_PREMISE_COMPONENT).length > 0

    const projects: Value[] = app.relApplicationToProject.edges.map((p: any) => ({
      label: p.node.factSheet.name,
      value: p.node.factSheet.id
    }))

    const parsed: Application = {
      id: app.id,
      snowId: app?.serviceNowId?.externalId || '',
      name: app.name,
      corpUnit: getCorpUnit(app.name),
      level: app.level,
      platform: app.plattform ? app.plattform : '',
      lifecycle: app.lifecycle?.asString.length > 0 ? app.lifecycle.asString : '-',
      domain,
      businessAreas,
      serviceBaseLineApproach,
      serviceBaseLineComment: app?.ServiceBaselineComment,
      hostingCH: false,
      projects,
      children: []
    }

    if (app.relToChild.edges.length > 1) {
      // contains childs

      app.relToChild.edges.forEach((child: any) => {
        const domainChild: Value | undefined = child.node.factSheet.relApplicationToDomain.edges.length > 0
          ? {
              value: child.node.factSheet.relApplicationToDomain.edges[0].node.factSheet.id,
              label: child.node.factSheet.relApplicationToDomain.edges[0].node.factSheet.displayName
            }
          : undefined

        const businessAreasChild: Value[] = child.node.factSheet.relApplicationToUserGroup.edges.flatMap((userGroup: any) => {
          const groups = []

          // Aktuelle UserGroup hinzufügen
          groups.push({
            label: userGroup.node.factSheet.name,
            value: userGroup.node.factSheet.id,
            level: userGroup.node.factSheet.level
          })

          // Parent UserGroups hinzufügen, falls vorhanden
          if (userGroup.node.factSheet.relToParent?.edges) {
            userGroup.node.factSheet.relToParent.edges.forEach((parent: any) => {
              groups.push({
                label: parent.node.factSheet.name,
                value: parent.node.factSheet.id,
                level: parent.node.factSheet.level
              })
            })
          }

          return groups
        })

        const serviceBaseLineApproachChild = child.node.factSheet.tags.filter((t: any) => t.tagGroup?.id === SERVICE_BASELINE_APPROACH_TAG_ID).length > 0
          ? child.node.factSheet.tags.filter((t: any) => t.tagGroup?.id === SERVICE_BASELINE_APPROACH_TAG_ID)[0].name
          : ''

        const projectsChild: Value[] = child.node.factSheet.relApplicationToProject.edges.map((p: any) => ({
          label: p.node.factSheet.name,
          value: p.node.factSheet.id
        }))

        if ((child.node.factSheet.lifecycle?.asString.length > 0 ? child.node.factSheet.lifecycle.asString : '-') !== 'endOfLife') {
          parsed.children!.push({
            id: child.node.factSheet.id,
            snowId: child.node.factSheet.serviceNowId || '',
            name: child.node.factSheet.name,
            corpUnit: getCorpUnit(child.node.factSheet.name),
            platform: child.node.factSheet.plattform ? child.node.factSheet.plattform : '',
            level: child.node.factSheet.level,
            lifecycle: child.node.factSheet.lifecycle?.asString.length > 0 ? child.node.factSheet.lifecycle.asString : '-',
            domain: domainChild,
            businessAreas: businessAreasChild,
            serviceBaseLineApproach: serviceBaseLineApproachChild,
            serviceBaseLineComment: child.node.factSheet?.ServiceBaselineComment,
            hostingCH: false,
            projects: projectsChild,
            children: undefined

          })
        }
      })
    }
    else {
      parsed.children = undefined
    }

    applications.push(parsed)
  })

  return applications
}
