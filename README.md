# Milestone Roadmap Report

Use this template for new custom reports with the built in filters of LeanIX

## Requirements
- PNPM >= 10.10.0
- NODE == v20.18.0
- lxr.json file in project folder with this content:



To get access to LeanIX, you have to create a `lxr.json` file with following format:

`{
  "host": "", 
  "workspace": "",
  "apitoken": "",
  "proxyURL": "",
  "localPort": ""
}`


## Project setup
This project was created with `pnpx create-lxr` ([details](https://docs-eam.leanix.net/docs/build-a-custom-report#getting-started))

1. `pnpm install`
2. Create `lxr.json` file

## Available scripts
### `pnpm run dev`
This command will start the local development server. Please make sure you have properly configured `lxr.json` first.

### `pnpm build`
Builds the report and outputs the build result into `dist` folder.

### `pnpm upload`
Uploads the report to the workspace configured in `lxr.json`